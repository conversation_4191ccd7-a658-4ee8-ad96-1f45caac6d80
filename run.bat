@echo off
echo ========================================
echo       تطبيق جمع الأخبار العراقية
echo ========================================
echo.

echo تحقق من تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo هل تريد إضافة مصادر موصى بها؟ (y/n)
set /p add_sources=
if /i "%add_sources%"=="y" (
    echo.
    echo اختر نوع المصادر:
    echo 1. سريعة فقط ^(5 مصادر^)
    echo 2. سريعة ومتوسطة ^(7 مصادر^)
    echo 3. جميع المصادر ^(9 مصادر^)
    set /p choice=اختر رقم ^(1-3^):

    if "!choice!"=="1" python add_recommended_sources.py fast
    if "!choice!"=="2" python add_recommended_sources.py medium
    if "!choice!"=="3" python add_recommended_sources.py all

    echo.
    pause
)

echo.
echo بدء تشغيل التطبيق...
echo يمكنك الوصول للتطبيق على: http://localhost:5008
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

python app.py

pause
