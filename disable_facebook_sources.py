#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تعطيل مصادر فيسبوك وإضافة بدائل أفضل
"""

import sqlite3
from datetime import datetime

def disable_facebook_sources():
    """تعطيل مصادر فيسبوك الحالية"""
    
    try:
        conn = sqlite3.connect('news_database.db')
        cursor = conn.cursor()
        
        # البحث عن مصادر فيسبوك
        cursor.execute("SELECT id, name, url FROM sources WHERE type = 'facebook' AND active = 1")
        facebook_sources = cursor.fetchall()
        
        if facebook_sources:
            print("📱 مصادر فيسبوك الموجودة:")
            for source_id, name, url in facebook_sources:
                print(f"  • {name} - {url}")
            
            # تعطيل مصادر فيسبوك
            cursor.execute("UPDATE sources SET active = 0 WHERE type = 'facebook'")
            disabled_count = cursor.rowcount
            
            print(f"\n⚠️  تم تعطيل {disabled_count} مصدر فيسبوك")
            print("💡 السبب: فيسبوك يحمي صفحاته من الـ scraping")
            
        else:
            print("ℹ️  لا توجد مصادر فيسبوك نشطة")
        
        conn.commit()
        conn.close()
        
        return len(facebook_sources)
        
    except Exception as e:
        print(f"❌ خطأ في تعطيل مصادر فيسبوك: {str(e)}")
        return 0

def add_alternative_sources():
    """إضافة مصادر بديلة محسنة"""
    
    # مصادر بديلة عالية الجودة
    alternative_sources = [
        {
            'name': 'شفق نيوز - الصفحة الرئيسية',
            'url': 'https://shafaq.com',
            'type': 'website'
        },
        {
            'name': 'شفق نيوز - أخبار العراق',
            'url': 'https://shafaq.com/ar/iraq',
            'type': 'website'
        },
        {
            'name': 'السومرية نيوز - العراق',
            'url': 'https://www.alsumaria.tv/news/iraq',
            'type': 'website'
        },
        {
            'name': 'وكالة الأنباء العراقية - أخبار',
            'url': 'https://www.ina.iq/news',
            'type': 'website'
        },
        {
            'name': 'الزمان - العراق',
            'url': 'https://www.azzaman.com/category/iraq',
            'type': 'website'
        },
        {
            'name': 'بغداد اليوم - أخبار',
            'url': 'https://baghdadtoday.news/news',
            'type': 'website'
        },
        {
            'name': 'الفلوجة نيوز',
            'url': 'https://www.alfalujanews.com',
            'type': 'website'
        }
    ]
    
    try:
        conn = sqlite3.connect('news_database.db')
        cursor = conn.cursor()
        
        added_count = 0
        skipped_count = 0
        
        print("\n🔄 إضافة مصادر بديلة...")
        
        for source in alternative_sources:
            # التحقق من عدم وجود المصدر مسبقاً
            cursor.execute('SELECT id FROM sources WHERE url = ?', (source['url'],))
            if cursor.fetchone():
                print(f"⚠️  موجود مسبقاً: {source['name']}")
                skipped_count += 1
                continue
            
            # إضافة المصدر
            cursor.execute('''
                INSERT INTO sources (name, url, type, active, created_at)
                VALUES (?, ?, ?, 1, ?)
            ''', (source['name'], source['url'], source['type'], datetime.now()))
            
            print(f"✅ تم إضافة: {source['name']}")
            added_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"\n📊 النتائج:")
        print(f"✅ تم إضافة {added_count} مصدر جديد")
        print(f"⚠️  تم تجاهل {skipped_count} مصدر موجود مسبقاً")
        
        return added_count
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المصادر البديلة: {str(e)}")
        return 0

def show_active_sources():
    """عرض المصادر النشطة الحالية"""
    
    try:
        conn = sqlite3.connect('news_database.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT type, COUNT(*) as count 
            FROM sources 
            WHERE active = 1 
            GROUP BY type
        ''')
        
        source_stats = cursor.fetchall()
        
        print(f"\n📈 إحصائيات المصادر النشطة:")
        total = 0
        for source_type, count in source_stats:
            print(f"  • {source_type}: {count} مصدر")
            total += count
        
        print(f"📊 المجموع: {total} مصدر نشط")
        
        # عرض أحدث المصادر
        cursor.execute('''
            SELECT name, type, url 
            FROM sources 
            WHERE active = 1 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        recent_sources = cursor.fetchall()
        
        if recent_sources:
            print(f"\n🆕 أحدث المصادر:")
            for name, source_type, url in recent_sources:
                print(f"  • {name} ({source_type})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في عرض المصادر: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🔧 تحسين مصادر الأخبار")
    print("=" * 50)
    
    # تعطيل مصادر فيسبوك
    disabled_count = disable_facebook_sources()
    
    # إضافة مصادر بديلة
    added_count = add_alternative_sources()
    
    # عرض الإحصائيات
    show_active_sources()
    
    print(f"\n🎉 تم الانتهاء!")
    print(f"📝 الملخص:")
    print(f"  • تم تعطيل {disabled_count} مصدر فيسبوك")
    print(f"  • تم إضافة {added_count} مصدر بديل")
    print(f"\n💡 النصائح:")
    print(f"  • المصادر الجديدة أسرع وأكثر موثوقية")
    print(f"  • لا توجد قيود أو حماية من الـ scraping")
    print(f"  • يمكنك اختبار المصادر من واجهة التطبيق")

if __name__ == "__main__":
    main()
