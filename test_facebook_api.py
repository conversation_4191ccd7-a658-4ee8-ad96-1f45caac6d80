#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار API فيسبوك
"""

import requests
import json

def test_facebook_source():
    """اختبار مصدر فيسبوك عبر API"""
    
    url = "http://localhost:5008/api/test-source"
    
    test_data = {
        "url": "https://www.facebook.com/shafaqnews",
        "type": "facebook"
    }
    
    try:
        print("🧪 اختبار مصدر فيسبوك...")
        print(f"الرابط: {test_data['url']}")
        print(f"النوع: {test_data['type']}")
        print()
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 النتيجة:")
        print(f"كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ النجاح: {result.get('success', False)}")
            print(f"📝 الرسالة: {result.get('message', 'لا توجد رسالة')}")
            
            if 'count' in result:
                print(f"📈 عدد المنشورات: {result['count']}")
            
            if 'iraq_related' in result:
                print(f"🇮🇶 المنشورات العراقية: {result['iraq_related']}")
                
        else:
            print(f"❌ فشل الاختبار: {response.status_code}")
            print(f"📝 الرسالة: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم")
        print("💡 تأكد من تشغيل التطبيق على http://localhost:5008")
        
    except requests.exceptions.Timeout:
        print("❌ خطأ: انتهت مهلة الانتظار")
        print("💡 قد يكون الموقع بطيء في الاستجابة")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")

if __name__ == "__main__":
    test_facebook_source()
