# تحسينات الأداء والسرعة

## 🚀 التحسينات المطبقة

### 1. نظام Cache متقدم
- **Cache في الذاكرة** للنتائج السريعة
- **Cache HTTP** للطلبات المتكررة
- **مدة صالحية**: 5 دقائق للمواقع، 10 دقائق لفيسبوك

### 2. معالجة متوازية محسنة
- **8 عمليات متوازية** بدلاً من 4
- **معالجة ذكية** للمصادر حسب السرعة
- **تجميع المصادر** حسب النوع

### 3. تصفية سريعة
- **فحص سريع** للكلمات المفتاحية
- **تجاهل المحتوى غير العراقي** مبكراً
- **حد أقصى 20 خبر** لكل مصدر

### 4. تحسين قاعدة البيانات
- **فهارس محسنة** للبحث السريع
- **تنظيف تلقائي** للأخبار القديمة
- **استعلامات محسنة** لتجنب التكرار

## ⚡ نصائح لتحسين السرعة

### 1. اختيار المصادر الصحيحة

#### مصادر سريعة (أقل من 5 ثواني)
```
✅ شفق نيوز - https://shafaq.com
✅ السومرية - https://www.alsumaria.tv  
✅ وكالة الأنباء العراقية - https://www.ina.iq
```

#### مصادر متوسطة (5-10 ثواني)
```
⚠️ الزمان - https://www.azzaman.com
⚠️ بغداد اليوم - https://baghdadtoday.news
```

#### مصادر بطيئة (تجنبها)
```
❌ المواقع التي تتطلب JavaScript
❌ المواقع المحمية بـ Cloudflare
❌ المواقع ذات الاستجابة البطيئة
```

### 2. تحسين إعدادات الجلب

#### إعدادات سريعة
```python
# في app.py
MAX_WORKERS = 8          # عدد العمليات المتوازية
TIMEOUT = 10             # مهلة الاتصال (ثواني)
MAX_RETRIES = 2          # عدد المحاولات
CACHE_DURATION = 300     # مدة الـ cache (ثواني)
```

#### إعدادات الجدولة
```python
# جلب كل 15 دقيقة للسرعة
AUTO_FETCH_INTERVAL = 15

# أو كل 30 دقيقة للتوازن
AUTO_FETCH_INTERVAL = 30
```

### 3. مراقبة الأداء

#### مؤشرات مهمة
- **وقت الجلب الإجمالي**: يجب أن يكون أقل من 60 ثانية
- **معدل النجاح**: يجب أن يكون أكثر من 80%
- **عدد الأخبار**: 10-50 خبر لكل دورة جلب

#### فحص السجلات
```bash
# مراقبة السجلات المباشرة
tail -f logs/app.log

# البحث عن الأخطاء
grep "ERROR" logs/app.log

# فحص أوقات الجلب
grep "تم جلب.*في.*ثانية" logs/app.log
```

## 🔧 حل المشاكل الشائعة

### 1. بطء في الجلب

#### الأسباب المحتملة:
- **مصادر بطيئة**: بعض المواقع تستجيب ببطء
- **اتصال ضعيف**: مشكلة في الإنترنت
- **حمولة زائدة**: كثرة المصادر

#### الحلول:
```python
# تقليل عدد المصادر
MAX_SOURCES_PER_BATCH = 5

# تقليل مهلة الانتظار
TIMEOUT = 8

# زيادة العمليات المتوازية
MAX_WORKERS = 10
```

### 2. استهلاك ذاكرة عالي

#### الأسباب:
- **تراكم الـ cache**: عدم تنظيف الذاكرة
- **كثرة البيانات**: حفظ محتوى كامل للأخبار

#### الحلول:
```python
# تنظيف الـ cache دورياً
def cleanup_cache():
    global memory_cache, cache_timestamps
    current_time = time.time()
    expired_keys = [
        key for key, timestamp in cache_timestamps.items()
        if current_time - timestamp > 600  # 10 دقائق
    ]
    for key in expired_keys:
        memory_cache.pop(key, None)
        cache_timestamps.pop(key, None)
```

### 3. أخطاء متكررة

#### أخطاء الاتصال:
```
ConnectionError: Failed to establish connection
```
**الحل**: زيادة مهلة الانتظار أو تغيير المصدر

#### أخطاء التحليل:
```
AttributeError: 'NoneType' object has no attribute
```
**الحل**: تحسين معالجة الأخطاء في الكود

#### أخطاء قاعدة البيانات:
```
sqlite3.OperationalError: database is locked
```
**الحل**: زيادة مهلة قاعدة البيانات

## 📊 قياس الأداء

### 1. مؤشرات السرعة

#### وقت الاستجابة
```python
# قياس وقت كل مصدر
start_time = time.time()
# ... عملية الجلب
end_time = time.time()
duration = end_time - start_time
print(f"المصدر استغرق {duration:.2f} ثانية")
```

#### معدل النجاح
```python
# حساب معدل النجاح
success_rate = (successful_sources / total_sources) * 100
print(f"معدل النجاح: {success_rate:.1f}%")
```

### 2. تحليل البيانات

#### أفضل المصادر أداءً
```sql
-- في قاعدة البيانات
SELECT source_id, COUNT(*) as news_count 
FROM news 
WHERE published_date = date('now') 
GROUP BY source_id 
ORDER BY news_count DESC;
```

#### أوقات الذروة
```sql
-- أفضل أوقات للجلب
SELECT strftime('%H', created_at) as hour, COUNT(*) as count
FROM news 
WHERE published_date = date('now')
GROUP BY hour 
ORDER BY count DESC;
```

## 🎯 إعدادات مُحسنة للبيئات المختلفة

### بيئة التطوير (سريعة)
```python
MAX_WORKERS = 4
TIMEOUT = 8
MAX_RETRIES = 1
AUTO_FETCH_INTERVAL = 60  # كل دقيقة للاختبار
```

### بيئة الإنتاج (متوازنة)
```python
MAX_WORKERS = 8
TIMEOUT = 15
MAX_RETRIES = 2
AUTO_FETCH_INTERVAL = 30  # كل 30 دقيقة
```

### بيئة الخادم القوي (شاملة)
```python
MAX_WORKERS = 12
TIMEOUT = 20
MAX_RETRIES = 3
AUTO_FETCH_INTERVAL = 15  # كل 15 دقيقة
```

## 🔄 صيانة دورية للأداء

### يومياً
- مراقبة السجلات للأخطاء
- فحص أوقات الاستجابة
- تنظيف الأخبار القديمة

### أسبوعياً
- تحليل أداء المصادر
- حذف المصادر البطيئة
- تحديث الكلمات المفتاحية

### شهرياً
- مراجعة شاملة للأداء
- تحسين إعدادات النظام
- إضافة مصادر جديدة سريعة
