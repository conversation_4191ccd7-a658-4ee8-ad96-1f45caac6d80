{% extends "base.html" %}

{% block title %}تأكيد حذف المصدر - أداة الأخبار العراقية الذكية{% endblock %}

{% block extra_css %}
<style>
    .confirm-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        max-width: 600px;
        margin: 0 auto;
    }
    .danger-header {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .source-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        border-left: 4px solid #e74c3c;
    }
    .btn-danger-custom {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        border: none;
        color: white;
        transition: all 0.3s ease;
    }
    .btn-danger-custom:hover {
        background: linear-gradient(45deg, #c0392b, #a93226);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
    }
    .btn-safe {
        background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        border: none;
        color: white;
        transition: all 0.3s ease;
    }
    .btn-safe:hover {
        background: linear-gradient(45deg, #7f8c8d, #6c7b7d);
        color: white;
        transform: translateY(-2px);
    }
    .warning-icon {
        font-size: 4rem;
        color: #e74c3c;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="confirm-card">
            <div class="card-header danger-header text-center p-4">
                <h3 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف المصدر
                </h3>
            </div>
            
            <div class="card-body text-center p-4">
                <!-- أيقونة تحذير -->
                <div class="mb-4">
                    <i class="fas fa-trash-alt warning-icon"></i>
                </div>
                
                <!-- رسالة التحذير -->
                <div class="alert alert-danger mb-4">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        تحذير مهم!
                    </h5>
                    <p class="mb-0">
                        أنت على وشك حذف هذا المصدر نهائياً. هذا الإجراء <strong>لا يمكن التراجع عنه</strong>.
                    </p>
                </div>
                
                <!-- معلومات المصدر -->
                <div class="source-info">
                    <h6 class="mb-3">
                        <i class="fas fa-info-circle me-2 text-danger"></i>
                        معلومات المصدر المراد حذفه:
                    </h6>
                    
                    <div class="row text-start">
                        <div class="col-md-6 mb-2">
                            <strong>الاسم:</strong>
                            <br>
                            <span class="text-muted">{{ source[1] }}</span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>النوع:</strong>
                            <br>
                            <span class="badge bg-info">{{ source[3] }}</span>
                        </div>
                        <div class="col-12 mb-2">
                            <strong>الرابط:</strong>
                            <br>
                            <a href="{{ source[2] }}" target="_blank" class="text-decoration-none text-muted">
                                {{ source[2] }}
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>الحالة:</strong>
                            <br>
                            <span class="badge {% if source[4] == 1 %}bg-success{% else %}bg-secondary{% endif %}">
                                {% if source[4] == 1 %}نشط{% else %}معطل{% endif %}
                            </span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ الإضافة:</strong>
                            <br>
                            <span class="text-muted">{{ source[5] if source[5] else 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- تحذيرات إضافية -->
                <div class="alert alert-warning mb-4">
                    <h6 class="mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ما سيحدث عند الحذف:
                    </h6>
                    <ul class="list-unstyled mb-0 text-start">
                        <li><i class="fas fa-times text-danger me-2"></i>سيتم حذف المصدر نهائياً من النظام</li>
                        <li><i class="fas fa-times text-danger me-2"></i>لن يتم جلب أخبار من هذا المصدر مستقبلاً</li>
                        <li><i class="fas fa-info text-info me-2"></i>الأخبار المجلوبة سابقاً ستبقى في النظام</li>
                        <li><i class="fas fa-undo text-warning me-2"></i>لا يمكن التراجع عن هذا الإجراء</li>
                    </ul>
                </div>
                
                <!-- أزرار الإجراء -->
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <a href="{{ url_for('sources') }}" class="btn btn-safe btn-lg px-4">
                        <i class="fas fa-arrow-left me-2"></i>
                        إلغاء والعودة
                    </a>
                    
                    <form method="POST" action="{{ url_for('delete_source', source_id=source[0]) }}" style="display: inline;">
                        <button type="submit" class="btn btn-danger-custom btn-lg px-4" onclick="return confirmDelete()">
                            <i class="fas fa-trash-alt me-2"></i>
                            تأكيد الحذف
                        </button>
                    </form>
                </div>
                
                <!-- نصائح -->
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>نصيحة:</strong> إذا كنت تريد إيقاف المصدر مؤقتاً فقط، يمكنك إلغاء تفعيله بدلاً من حذفه.
                    </small>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="text-center mt-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-question-circle me-2 text-info"></i>
                        بدائل أخرى للحذف
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('edit_source', source_id=source[0]) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit me-1"></i>
                                تعديل المصدر
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <form method="POST" action="{{ url_for('toggle_source', source_id=source[0]) }}" style="display: inline;">
                                <button type="submit" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-toggle-{% if source[4] == 1 %}off{% else %}on{% endif %} me-1"></i>
                                    {% if source[4] == 1 %}إلغاء التفعيل{% else %}تفعيل{% endif %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    return confirm(
        'هل أنت متأكد تماماً من حذف هذا المصدر؟\n\n' +
        'المصدر: {{ source[1] }}\n' +
        'الرابط: {{ source[2] }}\n\n' +
        'هذا الإجراء لا يمكن التراجع عنه!'
    );
}

// تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير على أزرار الخطر
    const dangerBtn = document.querySelector('.btn-danger-custom');
    if (dangerBtn) {
        dangerBtn.addEventListener('mouseenter', function() {
            this.innerHTML = '<i class="fas fa-skull-crossbones me-2"></i>حذف نهائي!';
        });
        
        dangerBtn.addEventListener('mouseleave', function() {
            this.innerHTML = '<i class="fas fa-trash-alt me-2"></i>تأكيد الحذف';
        });
    }
    
    // عد تنازلي للتفكير (اختياري)
    let countdown = 5;
    const countdownElement = document.createElement('div');
    countdownElement.className = 'alert alert-info mt-3';
    countdownElement.innerHTML = `
        <i class="fas fa-clock me-2"></i>
        فكر جيداً... <span id="countdown">${countdown}</span> ثوان للمراجعة
    `;
    
    // إضافة العد التنازلي (اختياري - يمكن تفعيله)
    // document.querySelector('.card-body').appendChild(countdownElement);
    
    // const timer = setInterval(() => {
    //     countdown--;
    //     document.getElementById('countdown').textContent = countdown;
    //     if (countdown <= 0) {
    //         clearInterval(timer);
    //         countdownElement.remove();
    //     }
    // }, 1000);
});
</script>
{% endblock %}
