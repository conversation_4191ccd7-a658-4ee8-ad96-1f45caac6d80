{% extends "base.html" %}

{% block title %}إضافة مصدر جديد - موسوعة أخبار العراق{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .form-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
    }

    .form-header h1 {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 10px;
    }

    .form-header p {
        color: #6c757d;
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid var(--border-color);
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .form-text {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
    }

    .source-type-card {
        border: 2px solid var(--border-color);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .source-type-card:hover {
        border-color: var(--secondary-color);
        background: rgba(52, 152, 219, 0.05);
    }

    .source-type-card.selected {
        border-color: var(--primary-color);
        background: rgba(44, 62, 80, 0.05);
    }

    .source-type-icon {
        font-size: 2rem;
        color: var(--secondary-color);
        margin-bottom: 10px;
    }

    .source-type-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 5px;
    }

    .source-type-description {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .preview-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-top: 30px;
        border-left: 4px solid var(--secondary-color);
    }

    .preview-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 15px;
    }

    .preview-item {
        display: flex;
        justify-content: between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .preview-item:last-child {
        border-bottom: none;
    }

    .preview-label {
        font-weight: 500;
        color: #6c757d;
        min-width: 100px;
    }

    .preview-value {
        color: var(--dark-text);
        flex-grow: 1;
    }

    .btn-submit {
        background: linear-gradient(45deg, var(--success-color), #2ecc71);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        color: white;
    }

    .btn-cancel {
        background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        color: white;
    }

    @media (max-width: 768px) {
        .form-container {
            margin: 10px;
            padding: 20px;
        }
        
        .source-type-card {
            padding: 15px;
        }
        
        .btn-submit, .btn-cancel {
            width: 100%;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <!-- Form Header -->
    <div class="form-header">
        <h1>
            <i class="fas fa-plus-circle me-3"></i>
            إضافة مصدر أخبار جديد
        </h1>
        <p>أضف مصدر أخبار عراقي جديد لتوسيع شبكة الأخبار</p>
    </div>

    <!-- Form -->
    <form method="POST" action="{{ url_for('add_source') }}" id="addSourceForm">
        <!-- Source Name -->
        <div class="form-group">
            <label for="name" class="form-label">
                <i class="fas fa-tag me-2"></i>
                اسم المصدر
            </label>
            <input type="text" class="form-control" id="name" name="name" required
                   placeholder="مثال: شفق نيوز، السومرية، الفرات نيوز">
            <div class="form-text">اسم المصدر كما سيظهر في النظام</div>
        </div>

        <!-- Source URL -->
        <div class="form-group">
            <label for="url" class="form-label">
                <i class="fas fa-link me-2"></i>
                رابط المصدر
            </label>
            <input type="url" class="form-control" id="url" name="url" required
                   placeholder="https://example.com">
            <div class="form-text">الرابط الكامل للموقع أو الصفحة</div>
        </div>

        <!-- Source Type -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-layer-group me-2"></i>
                نوع المصدر
            </label>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="source-type-card" data-type="website">
                        <div class="text-center">
                            <div class="source-type-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="source-type-title">موقع ويب</div>
                            <div class="source-type-description">
                                مواقع الأخبار التقليدية
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="source-type-card" data-type="facebook">
                        <div class="text-center">
                            <div class="source-type-icon">
                                <i class="fab fa-facebook"></i>
                            </div>
                            <div class="source-type-title">فيسبوك</div>
                            <div class="source-type-description">
                                صفحات فيسبوك الإخبارية
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="source-type-card" data-type="rss">
                        <div class="text-center">
                            <div class="source-type-icon">
                                <i class="fas fa-rss"></i>
                            </div>
                            <div class="source-type-title">RSS Feed</div>
                            <div class="source-type-description">
                                تغذية RSS للأخبار
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <input type="hidden" id="type" name="type" required>
            <div class="form-text">اختر نوع المصدر المناسب</div>
        </div>

        <!-- Category -->
        <div class="form-group">
            <label for="category" class="form-label">
                <i class="fas fa-folder me-2"></i>
                فئة المصدر
            </label>
            <select class="form-select" id="category" name="category">
                <option value="أخبار عامة">أخبار عامة</option>
                <option value="وكالة رسمية">وكالة رسمية</option>
                <option value="صحيفة رسمية">صحيفة رسمية</option>
                <option value="قناة تلفزيونية">قناة تلفزيونية</option>
                <option value="موقع إخباري">موقع إخباري</option>
                <option value="صحيفة إلكترونية">صحيفة إلكترونية</option>
                <option value="وسائل التواصل">وسائل التواصل</option>
            </select>
            <div class="form-text">تصنيف المصدر حسب نوعه</div>
        </div>

        <!-- Description -->
        <div class="form-group">
            <label for="description" class="form-label">
                <i class="fas fa-info-circle me-2"></i>
                وصف المصدر (اختياري)
            </label>
            <textarea class="form-control" id="description" name="description" rows="3"
                      placeholder="وصف مختصر عن المصدر وتخصصه"></textarea>
            <div class="form-text">معلومات إضافية عن المصدر</div>
        </div>

        <!-- Preview Section -->
        <div class="preview-section" id="previewSection" style="display: none;">
            <div class="preview-title">
                <i class="fas fa-eye me-2"></i>
                معاينة المصدر
            </div>
            <div id="previewContent">
                <!-- سيتم ملء المعاينة هنا -->
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <a href="{{ url_for('admin') }}" class="btn btn-cancel">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            
            <div>
                <button type="button" class="btn btn-outline-info me-2" onclick="previewSource()">
                    <i class="fas fa-eye me-2"></i>
                    معاينة
                </button>
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-plus me-2"></i>
                    إضافة المصدر
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اختيار نوع المصدر
document.querySelectorAll('.source-type-card').forEach(card => {
    card.addEventListener('click', function() {
        // إزالة التحديد من جميع البطاقات
        document.querySelectorAll('.source-type-card').forEach(c => {
            c.classList.remove('selected');
        });
        
        // تحديد البطاقة المختارة
        this.classList.add('selected');
        
        // تحديث القيمة المخفية
        const type = this.dataset.type;
        document.getElementById('type').value = type;
        
        // تحديث placeholder الرابط حسب النوع
        updateUrlPlaceholder(type);
        
        // تحديث المعاينة
        updatePreview();
    });
});

// تحديث placeholder الرابط
function updateUrlPlaceholder(type) {
    const urlInput = document.getElementById('url');
    
    switch(type) {
        case 'website':
            urlInput.placeholder = 'https://shafaq.com';
            break;
        case 'facebook':
            urlInput.placeholder = 'https://www.facebook.com/pagename';
            break;
        case 'rss':
            urlInput.placeholder = 'https://example.com/rss.xml';
            break;
        default:
            urlInput.placeholder = 'https://example.com';
    }
}

// معاينة المصدر
function previewSource() {
    updatePreview();
    
    const previewSection = document.getElementById('previewSection');
    previewSection.style.display = 'block';
    
    // تمرير سلس للمعاينة
    previewSection.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
    });
}

// تحديث المعاينة
function updatePreview() {
    const name = document.getElementById('name').value || 'غير محدد';
    const url = document.getElementById('url').value || 'غير محدد';
    const type = document.getElementById('type').value || 'غير محدد';
    const category = document.getElementById('category').value || 'غير محدد';
    const description = document.getElementById('description').value || 'غير محدد';
    
    const typeNames = {
        'website': 'موقع ويب',
        'facebook': 'فيسبوك',
        'rss': 'RSS Feed'
    };
    
    const previewContent = document.getElementById('previewContent');
    previewContent.innerHTML = `
        <div class="preview-item">
            <div class="preview-label">الاسم:</div>
            <div class="preview-value">${name}</div>
        </div>
        <div class="preview-item">
            <div class="preview-label">الرابط:</div>
            <div class="preview-value">${url}</div>
        </div>
        <div class="preview-item">
            <div class="preview-label">النوع:</div>
            <div class="preview-value">${typeNames[type] || type}</div>
        </div>
        <div class="preview-item">
            <div class="preview-label">الفئة:</div>
            <div class="preview-value">${category}</div>
        </div>
        <div class="preview-item">
            <div class="preview-label">الوصف:</div>
            <div class="preview-value">${description}</div>
        </div>
    `;
}

// تحديث المعاينة عند تغيير القيم
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['name', 'url', 'category', 'description'];
    
    inputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', updatePreview);
        }
    });
    
    // التحقق من صحة النموذج
    const form = document.getElementById('addSourceForm');
    form.addEventListener('submit', function(e) {
        const type = document.getElementById('type').value;
        
        if (!type) {
            e.preventDefault();
            alert('يرجى اختيار نوع المصدر');
            return false;
        }
        
        // تأكيد الإضافة
        const name = document.getElementById('name').value;
        if (!confirm(`هل تريد إضافة المصدر "${name}"؟`)) {
            e.preventDefault();
            return false;
        }
    });
});

// تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تدرجي لظهور النموذج
    const formContainer = document.querySelector('.form-container');
    formContainer.style.opacity = '0';
    formContainer.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        formContainer.style.transition = 'all 0.5s ease';
        formContainer.style.opacity = '1';
        formContainer.style.transform = 'translateY(0)';
    }, 100);
});
</script>
{% endblock %}
