# إعداد Facebook API للحصول على الأخبار

## الخطوات المطلوبة:

### 1. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook Developers](https://developers.facebook.com/)
2. انقر على "My Apps" ثم "Create App"
3. اختر "Business" كنوع التطبيق
4. أدخل اسم التطبيق والبريد الإلكتروني

### 2. إضافة منتج Facebook Login
1. في لوحة التحكم، انقر على "Add Product"
2. اختر "Facebook Login" وانقر "Set Up"
3. اختر "Web" كمنصة

### 3. الحصول على Access Token
1. اذهب إلى [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
2. اختر تطبيقك من القائمة المنسدلة
3. انقر على "Generate Access Token"
4. اختر الصلاحيات المطلوبة:
   - `pages_read_engagement`
   - `pages_show_list`
   - `public_profile`

### 4. تحويل إلى Long-lived Token
```bash
curl -i -X GET "https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id={app-id}&client_secret={app-secret}&fb_exchange_token={short-lived-token}"
```

### 5. ضبط متغيرات البيئة
```bash
# في Windows
set FACEBOOK_ACCESS_TOKEN=your_long_lived_token_here

# في Linux/Mac
export FACEBOOK_ACCESS_TOKEN=your_long_lived_token_here
```

## اختبار الإعداد:

### اختبار صفحة فيسبوك:
```bash
curl "https://graph.facebook.com/v19.0/{page-id}/posts?access_token={your-token}&fields=message,created_time"
```

## ملاحظات مهمة:

⚠️ **قيود Facebook API:**
- يمكن الوصول فقط للصفحات العامة
- لا يمكن الوصول للمجموعات الخاصة
- معدل الطلبات محدود (200 طلب/ساعة للتطبيقات الجديدة)

⚠️ **أمان Token:**
- لا تشارك الـ Access Token مع أحد
- استخدم متغيرات البيئة فقط
- جدد الـ Token كل 60 يوم

## استكشاف الأخطاء:

### خطأ "Invalid OAuth access token"
- تأكد من صحة الـ Token
- تحقق من انتهاء صلاحية الـ Token
- تأكد من الصلاحيات المطلوبة

### خطأ "Page not found"
- تأكد من أن الصفحة عامة
- تحقق من صحة معرف الصفحة
- تأكد من أن الصفحة ليست محذوفة

### خطأ "Rate limit exceeded"
- قلل من عدد الطلبات
- انتظر قبل إعادة المحاولة
- استخدم caching للنتائج
