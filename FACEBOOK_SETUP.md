# جلب الأخبار من فيسبوك عن طريق الزحف (Web Scraping)

## كيف يعمل النظام:

النظام يستخدم **Web Scraping** لجلب المنشورات من الصفحات العامة في فيسبوك بدلاً من Facebook API.

## المتطلبات:

✅ **لا يحتاج إلى API Token**
✅ **يعمل مع الصفحات العامة فقط**
✅ **تلقائي بالكامل**

## كيفية إضافة مصدر فيسبوك:

### 1. الحصول على رابط الصفحة
```
https://www.facebook.com/IraqiNews
https://facebook.com/baghdadtoday
https://m.facebook.com/iraqnewsagency
```

### 2. إضافة المصدر في التطبيق
1. اذهب إلى صفحة "المصادر"
2. انقر "إضافة مصدر جديد"
3. أدخل:
   - **الاسم**: اسم الصفحة
   - **الرابط**: رابط الصفحة
   - **النوع**: اختر "facebook"

### 3. اختبار المصدر
- انقر "اختبار المصدر" للتأكد من عمله
- سيظهر عدد المنشورات الموجودة
- سيظهر عدد المنشورات المتعلقة بالعراق

## أمثلة على الصفحات المدعومة:

### صفحات الأخبار العراقية:
- `https://www.facebook.com/IraqiNews`
- `https://www.facebook.com/AlSumaria`
- `https://www.facebook.com/AlArabiya.Iraq`
- `https://www.facebook.com/rudawnews`

## المميزات:

✅ **تصفية ذكية**: يجلب فقط المنشورات المتعلقة بالعراق
✅ **استخراج التاريخ**: يحاول استخراج تاريخ النشر الفعلي
✅ **تجنب التكرار**: لا يحفظ نفس المنشور مرتين
✅ **معالجة الأخطاء**: يتعامل مع مشاكل الشبكة والحظر

## القيود والملاحظات:

⚠️ **الصفحات العامة فقط**: لا يمكن الوصول للمجموعات الخاصة
⚠️ **معدل محدود**: يجلب كل 30 دقيقة لتجنب الحظر
⚠️ **تغيير فيسبوك**: قد يتوقف إذا غير فيسبوك بنية الصفحات

## استكشاف الأخطاء:

### "لم يتم العثور على منشورات"
- تأكد من أن الصفحة عامة
- تحقق من صحة الرابط
- جرب الرابط في المتصفح

### "خطأ HTTP 403/429"
- الصفحة محمية أو محظورة
- انتظر قليلاً وأعد المحاولة
- استخدم رابط النسخة المبسطة (m.facebook.com)

### "لا توجد أخبار عراقية"
- الصفحة لا تنشر محتوى عراقي
- أضف كلمات مفتاحية عراقية في الإعدادات
- تحقق من المنشورات الحديثة في الصفحة

## نصائح للحصول على أفضل النتائج:

1. **استخدم الصفحات الإخبارية الرسمية**
2. **تأكد من أن الصفحة تنشر بانتظام**
3. **اختبر المصدر قبل الإضافة**
4. **راقب السجلات للتأكد من عمل المصدر**
