{% extends "base.html" %}

{% block title %}الرئيسية - أداة الأخبار العراقية الذكية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body text-center">
                <h1 class="display-4 mb-4">
                    <i class="fas fa-newspaper text-primary me-3"></i>
                    أداة الأخبار العراقية الذكية
                </h1>
                <p class="lead mb-4">
                    أداة متطورة لجمع وعرض الأخبار العراقية النصية من مصادر متعددة باستخدام الذكاء الاصطناعي
                </p>
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>نصوص فقط:</strong> يركز التطبيق على جلب الأخبار النصية فقط (بدون صور أو فيديوهات) لضمان السرعة والدقة في التصنيف والتحليل
                </div>
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <i class="fas fa-file-text fa-3x text-primary mb-3"></i>
                                <h5>نصوص فقط</h5>
                                <p>جلب الأخبار النصية فقط بدون صور أو فيديوهات للسرعة والدقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <i class="fas fa-tags fa-3x text-success mb-3"></i>
                                <h5>تصنيف ذكي</h5>
                                <p>تصنيف تلقائي للأخبار إلى 32 فئة مع نظام أولويات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <i class="fas fa-filter fa-3x text-warning mb-3"></i>
                                <h5>تصفية متقدمة</h5>
                                <p>تصفية للعراق فقط مع استبعاد كردستان والفن والرياضة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الصفحات المحسنة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white;">
            <div class="card-header" style="background: rgba(255,255,255,0.1); border: none;">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-star me-2"></i>
                    الصفحات المحسنة الجديدة
                    <i class="fas fa-star ms-2"></i>
                </h4>
            </div>
            <div class="card-body">
                <p class="text-center mb-4">
                    <strong>جديد!</strong> صفحات محسنة مع تصميم عصري وميزات متقدمة
                </p>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100" style="background: rgba(255,255,255,0.95); color: #2c3e50;">
                            <div class="card-body text-center">
                                <i class="fas fa-newspaper fa-3x text-primary mb-3"></i>
                                <h5>الأخبار المحسنة</h5>
                                <p class="small">فلاتر متقدمة، تصفح محسن، إحصائيات مفصلة</p>
                                <a href="/news-enhanced" class="btn btn-primary btn-sm">
                                    <i class="fas fa-star me-1"></i>
                                    جرب الآن
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100" style="background: rgba(255,255,255,0.95); color: #2c3e50;">
                            <div class="card-body text-center">
                                <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                                <h5>إدارة المصادر المحسنة</h5>
                                <p class="small">اختبار تفاعلي، إحصائيات شاملة، إدارة متقدمة</p>
                                <a href="/sources-enhanced" class="btn btn-success btn-sm">
                                    <i class="fas fa-star me-1"></i>
                                    جرب الآن
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rss me-2"></i>
                    إدارة مصادر الأخبار
                </h5>
            </div>
            <div class="card-body">
                <p>إضافة وإدارة مصادر الأخبار العراقية</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('sources') }}" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>إدارة المصادر (عادي)
                    </a>
                    <a href="/sources-enhanced" class="btn btn-warning">
                        <i class="fas fa-star me-2"></i>إدارة المصادر المحسنة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-newspaper me-2"></i>
                    عرض الأخبار
                </h5>
            </div>
            <div class="card-body">
                <p>عرض الأخبار العراقية المجلوبة لليوم</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('news') }}" class="btn btn-success">
                        <i class="fas fa-eye me-2"></i>عرض الأخبار (عادي)
                    </a>
                    <a href="/news-enhanced" class="btn btn-warning">
                        <i class="fas fa-star me-2"></i>الأخبار المحسنة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sync me-2"></i>
                    جلب الأخبار الآن
                </h5>
            </div>
            <div class="card-body">
                <p>قم بجلب أحدث الأخبار من جميع المصادر المفعلة</p>
                <button id="fetchNewsBtn" class="btn btn-primary btn-lg">
                    <i class="fas fa-download me-2"></i>جلب الأخبار الآن
                </button>

                <div id="loading" class="loading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري جلب الأخبار...</p>
                </div>

                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary" id="sourcesCount">0</h4>
                        <p>مصادر الأخبار</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success" id="todayNewsCount">0</h4>
                        <p>أخبار اليوم</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info" id="iraqNewsCount">0</h4>
                        <p>أخبار عراقية</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning" id="currentDate">--</h4>
                        <p>التاريخ الحالي</p>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <strong>آخر تحديث:</strong> <span id="lastUpdate">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات التصنيفات والأولويات -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات التصنيفات
                </h5>
            </div>
            <div class="card-body">
                <div id="categoriesStats">
                    <p class="text-muted text-center">جاري تحميل الإحصائيات...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    إحصائيات الأولويات
                </h5>
            </div>
            <div class="card-body">
                <div id="prioritiesStats">
                    <p class="text-muted text-center">جاري تحميل الإحصائيات...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('fetchNewsBtn').addEventListener('click', function() {
    const btn = this;
    const loading = document.getElementById('loading');
    const result = document.getElementById('result');

    btn.disabled = true;
    loading.style.display = 'block';
    result.innerHTML = '';

    fetch('/api/fetch-news', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        loading.style.display = 'none';
        btn.disabled = false;

        if (data.success) {
            result.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    تم جلب ${data.count} خبر جديد بنجاح!
                </div>
            `;
            updateStats();
        } else {
            result.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ: ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        loading.style.display = 'none';
        btn.disabled = false;
        result.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                حدث خطأ في الاتصال
            </div>
        `;
    });
});

function updateStats() {
    fetch('/api/stats')
    .then(response => response.json())
    .then(data => {
        document.getElementById('sourcesCount').textContent = data.sources_count;
        document.getElementById('todayNewsCount').textContent = data.today_news_count;
        document.getElementById('iraqNewsCount').textContent = data.iraq_news_count;

        // عرض التاريخ الحالي
        const currentDate = new Date();
        const arabicDate = currentDate.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('currentDate').textContent = arabicDate;

        // عرض آخر تحديث
        if (data.last_update && data.last_update !== 'لا يوجد') {
            const updateDate = new Date(data.last_update);
            const arabicUpdateDate = updateDate.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('lastUpdate').textContent = arabicUpdateDate;
        } else {
            document.getElementById('lastUpdate').textContent = 'لا يوجد';
        }

        // تحديث إحصائيات التصنيفات
        updateCategoriesStats(data.categories_stats);

        // تحديث إحصائيات الأولويات
        updatePrioritiesStats(data.priorities_stats);
    });
}

function updateCategoriesStats(categories) {
    const container = document.getElementById('categoriesStats');

    if (!categories || categories.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد أخبار مصنفة</p>';
        return;
    }

    let html = '';
    categories.forEach(([category, count]) => {
        const percentage = Math.round((count / categories.reduce((sum, [, c]) => sum + c, 0)) * 100);
        html += `
            <div class="mb-2">
                <div class="d-flex justify-content-between">
                    <span>${category}</span>
                    <span class="badge bg-primary">${count}</span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updatePrioritiesStats(priorities) {
    const container = document.getElementById('prioritiesStats');

    if (!priorities || priorities.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد أخبار مصنفة</p>';
        return;
    }

    let html = '';
    const priorityColors = {
        'عالية': 'bg-danger',
        'متوسطة': 'bg-warning',
        'عادية': 'bg-success'
    };

    priorities.forEach(([priority, count]) => {
        const colorClass = priorityColors[priority] || 'bg-secondary';
        const percentage = Math.round((count / priorities.reduce((sum, [, c]) => sum + c, 0)) * 100);
        html += `
            <div class="mb-2">
                <div class="d-flex justify-content-between">
                    <span>${priority}</span>
                    <span class="badge ${colorClass}">${count}</span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar ${colorClass}" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// تحديث الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateStats);
</script>
{% endblock %}
