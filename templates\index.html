{% extends "base.html" %}

{% block title %}موسوعة أخبار العراق - الصفحة الرئيسية{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.9), rgba(52, 152, 219, 0.9));
        color: white;
        padding: 40px 0;
        border-radius: 20px;
        margin-bottom: 30px;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 30px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .sources-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .source-card {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .source-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .source-info {
        flex-grow: 1;
    }

    .source-footer {
        margin-top: auto;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .category-badge {
        background: var(--secondary-color);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 500;
    }

    .fetch-button {
        position: fixed;
        bottom: 30px;
        left: 30px;
        z-index: 1000;
        border-radius: 50px;
        padding: 15px 25px;
        font-size: 1.1rem;
        font-weight: 600;
        box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    }

    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .search-input {
        border-radius: 25px;
        border: 2px solid var(--border-color);
        padding: 12px 20px;
        font-size: 1.1rem;
    }

    .search-input:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .sources-grid {
            grid-template-columns: 1fr;
        }
        
        .fetch-button {
            bottom: 20px;
            left: 20px;
            padding: 12px 20px;
            font-size: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <h1 class="hero-title">
            <i class="fas fa-newspaper me-3"></i>
            موسوعة أخبار العراق
        </h1>
        <p class="hero-subtitle">
            مصدرك الموثوق للأخبار العراقية من جميع المصادر المحلية والعربية
        </p>
        <div class="current-time h5"></div>
    </div>
</div>

<!-- Search Section -->
<div class="search-section">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="input-group">
                <span class="input-group-text bg-transparent border-end-0">
                    <i class="fas fa-search text-muted"></i>
                </span>
                <input type="text" class="form-control search-input border-start-0" 
                       placeholder="ابحث في المصادر..." id="sourceSearch">
            </div>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-primary" onclick="fetchAllNews()">
                <i class="fas fa-sync-alt me-2"></i>
                جلب أخبار جديدة
            </button>
        </div>
    </div>
</div>

<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stats-card">
        <div class="stats-number">{{ active_sources }}</div>
        <div class="stats-label">
            <i class="fas fa-rss me-1"></i>
            مصادر نشطة
        </div>
    </div>
    
    <div class="stats-card">
        <div class="stats-number">{{ today_news }}</div>
        <div class="stats-label">
            <i class="fas fa-newspaper me-1"></i>
            أخبار اليوم
        </div>
    </div>
    
    <div class="stats-card">
        <div class="stats-number">{{ total_news }}</div>
        <div class="stats-label">
            <i class="fas fa-archive me-1"></i>
            إجمالي الأخبار
        </div>
    </div>
    
    <div class="stats-card">
        <div class="stats-number" id="liveStats">--</div>
        <div class="stats-label">
            <i class="fas fa-clock me-1"></i>
            آخر تحديث
        </div>
    </div>
</div>

<!-- Sources Section -->
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-globe me-2 text-primary"></i>
            مصادر الأخبار العراقية
            <small class="text-muted">({{ sources|length }} مصدر)</small>
        </h2>
    </div>
</div>

<!-- Sources Grid -->
<div class="sources-grid" id="sourcesGrid">
    {% for source in sources %}
    <div class="source-card" data-category="{{ source[4] }}" data-name="{{ source[1] }}">
        <div class="source-header">
            {% if source[6] %}
                <img src="{{ source[6] }}" alt="{{ source[1] }}" class="source-icon" 
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiByeD0iOCIgZmlsbD0iIzM0OThkYiIvPgo8dGV4dCB4PSIyNCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnt7IHNvdXJjZVsxXVswXSB9fTwvdGV4dD4KPHN2Zz4='">
            {% else %}
                <div class="source-icon d-flex align-items-center justify-content-center bg-primary text-white fw-bold">
                    {{ source[1][0] }}
                </div>
            {% endif %}
            
            <div class="source-info">
                <div class="source-name">{{ source[1] }}</div>
                <div class="source-category">{{ source[4] }}</div>
            </div>
        </div>
        
        <div class="mb-3">
            <small class="text-muted">
                <i class="fas fa-link me-1"></i>
                {{ source[2][:40] }}{% if source[2]|length > 40 %}...{% endif %}
            </small>
        </div>
        
        <div class="source-footer">
            <div>
                <span class="category-badge">{{ source[3] }}</span>
            </div>
            <div>
                <span class="news-count">{{ source[7] }} خبر</span>
            </div>
        </div>
        
        <div class="mt-3">
            <div class="btn-group w-100" role="group">
                <a href="{{ url_for('source_news', source_id=source[0]) }}" 
                   class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>
                    عرض الأخبار
                </a>
                <button class="btn btn-outline-success btn-sm" 
                        onclick="fetchSourceNews({{ source[0] }}, '{{ source[1] }}')">
                    <i class="fas fa-sync me-1"></i>
                    جلب
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not sources %}
<div class="text-center py-5">
    <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد مصادر نشطة</h4>
    <p class="text-muted">يرجى إضافة مصادر أخبار من لوحة الإدارة</p>
    <a href="{{ url_for('admin') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة مصادر
    </a>
</div>
{% endif %}

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner mb-3"></div>
                <h5 id="loadingText">جاري جلب الأخبار...</h5>
                <p class="text-muted mb-0" id="loadingDetails">يرجى الانتظار</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// البحث في المصادر
document.getElementById('sourceSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const sourceCards = document.querySelectorAll('.source-card');
    
    sourceCards.forEach(card => {
        const name = card.dataset.name.toLowerCase();
        const category = card.dataset.category.toLowerCase();
        
        if (name.includes(searchTerm) || category.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// جلب أخبار جميع المصادر
function fetchAllNews() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
    
    document.getElementById('loadingText').textContent = 'جاري جلب الأخبار من جميع المصادر...';
    document.getElementById('loadingDetails').textContent = 'قد يستغرق هذا عدة دقائق';
    
    fetch('/api/fetch-news', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        modal.hide();
        
        if (data.success) {
            alert('تم بدء عملية جلب الأخبار بنجاح!\nسيتم تحديث الصفحة تلقائياً.');
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        modal.hide();
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// جلب أخبار مصدر واحد
function fetchSourceNews(sourceId, sourceName) {
    if (!confirm(`هل تريد جلب أخبار جديدة من ${sourceName}؟`)) {
        return;
    }
    
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الجلب...';
    
    // محاكاة عملية الجلب (يمكن تطويرها لاحقاً)
    setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalText;
        alert(`تم جلب أخبار جديدة من ${sourceName}`);
    }, 3000);
}

// تحديث الإحصائيات المباشرة
function updateLiveStats() {
    fetch('/api/stats')
    .then(response => response.json())
    .then(data => {
        if (data.last_update) {
            const lastUpdate = new Date(data.last_update);
            const now = new Date();
            const diffMinutes = Math.floor((now - lastUpdate) / (1000 * 60));
            
            let timeText = '';
            if (diffMinutes < 1) {
                timeText = 'الآن';
            } else if (diffMinutes < 60) {
                timeText = `${diffMinutes} دقيقة`;
            } else {
                const diffHours = Math.floor(diffMinutes / 60);
                timeText = `${diffHours} ساعة`;
            }
            
            document.getElementById('liveStats').textContent = timeText;
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث الإحصائيات:', error);
    });
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateLiveStats, 60000);
updateLiveStats();

// تأثيرات تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تدرجي لظهور البطاقات
    const cards = document.querySelectorAll('.source-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
