{% extends "base.html" %}

{% block title %}إدارة المصادر المحسنة - أداة الأخبار العراقية الذكية{% endblock %}

{% block extra_css %}
<style>
    .source-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .source-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .source-type-badge {
        font-size: 0.8em;
        padding: 6px 12px;
        border-radius: 15px;
        font-weight: 500;
    }
    .source-status-active {
        background: linear-gradient(45deg, #27ae60, #2ecc71);
        color: white;
    }
    .source-status-inactive {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    .action-btn {
        margin: 2px;
        border-radius: 8px;
        transition: all 0.2s;
    }
    .action-btn:hover {
        transform: scale(1.05);
    }
    .test-result {
        margin-top: 10px;
        padding: 8px;
        border-radius: 8px;
        font-size: 0.85em;
    }
    .test-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .test-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .test-loading {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- إحصائيات سريعة -->
        <div class="stats-card">
            <div class="row text-center">
                <div class="col-md-3">
                    <h3 class="mb-1">{{ active_count }}</h3>
                    <small><i class="fas fa-check-circle me-1"></i>مصادر نشطة</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ inactive_count }}</h3>
                    <small><i class="fas fa-times-circle me-1"></i>مصادر معطلة</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ active_count + inactive_count }}</h3>
                    <small><i class="fas fa-globe me-1"></i>إجمالي المصادر</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ type_stats|length }}</h3>
                    <small><i class="fas fa-layer-group me-1"></i>أنواع مختلفة</small>
                </div>
            </div>
        </div>

        <!-- فلاتر وإجراءات -->
        <div class="filter-card p-4 mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-3">
                        <i class="fas fa-filter text-primary me-2"></i>
                        تصفية وإدارة المصادر
                    </h5>
                    <form method="GET" action="/sources" class="row">
                        <div class="col-md-4 mb-2">
                            <label class="form-label small">نوع المصدر</label>
                            <select class="form-select" name="type" onchange="this.form.submit()">
                                <option value="">جميع الأنواع</option>
                                <option value="website" {% if current_type == 'website' %}selected{% endif %}>
                                    <i class="fas fa-globe"></i> مواقع ويب
                                </option>
                                <option value="facebook" {% if current_type == 'facebook' %}selected{% endif %}>
                                    <i class="fab fa-facebook"></i> فيسبوك
                                </option>
                                <option value="telegram" {% if current_type == 'telegram' %}selected{% endif %}>
                                    <i class="fab fa-telegram"></i> تلغرام
                                </option>
                                <option value="rss" {% if current_type == 'rss' %}selected{% endif %}>
                                    <i class="fas fa-rss"></i> RSS
                                </option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label small">حالة المصدر</label>
                            <select class="form-select" name="status" onchange="this.form.submit()">
                                <option value="">جميع الحالات</option>
                                <option value="active" {% if current_status == 'active' %}selected{% endif %}>
                                    <i class="fas fa-check"></i> نشط
                                </option>
                                <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>
                                    <i class="fas fa-times"></i> معطل
                                </option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label small">&nbsp;</label>
                            <div class="d-grid">
                                <a href="/sources" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مصدر جديد
                        </button>
                        <button class="btn btn-success" onclick="testAllSources()">
                            <i class="fas fa-vial me-2"></i>
                            اختبار جميع المصادر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الأنواع -->
        {% if type_stats %}
        <div class="row mb-4">
            {% for type_name, count in type_stats %}
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <div class="mb-2">
                            {% if type_name == 'website' %}
                                <i class="fas fa-globe fa-2x text-primary"></i>
                            {% elif type_name == 'facebook' %}
                                <i class="fab fa-facebook fa-2x text-primary"></i>
                            {% elif type_name == 'telegram' %}
                                <i class="fab fa-telegram fa-2x text-primary"></i>
                            {% elif type_name == 'rss' %}
                                <i class="fas fa-rss fa-2x text-primary"></i>
                            {% else %}
                                <i class="fas fa-question fa-2x text-muted"></i>
                            {% endif %}
                        </div>
                        <h4 class="text-primary mb-1">{{ count }}</h4>
                        <p class="mb-0 small text-muted">{{ type_name }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- المصادر -->
        {% if sources %}
            <div class="row">
                {% for source in sources %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card source-card h-100" id="source-{{ source[0] }}">
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="card-title mb-0 flex-grow-1">{{ source[1] }}</h6>
                                <div class="d-flex gap-1">
                                    <span class="source-type-badge bg-info text-white">
                                        {% if source[3] == 'website' %}
                                            <i class="fas fa-globe me-1"></i>
                                        {% elif source[3] == 'facebook' %}
                                            <i class="fab fa-facebook me-1"></i>
                                        {% elif source[3] == 'telegram' %}
                                            <i class="fab fa-telegram me-1"></i>
                                        {% elif source[3] == 'rss' %}
                                            <i class="fas fa-rss me-1"></i>
                                        {% endif %}
                                        {{ source[3] }}
                                    </span>
                                </div>
                            </div>
                            
                            <p class="card-text text-muted small mb-3">
                                <i class="fas fa-link me-1"></i>
                                <a href="{{ source[2] }}" target="_blank" class="text-decoration-none" title="{{ source[2] }}">
                                    {{ source[2][:35] }}{% if source[2]|length > 35 %}...{% endif %}
                                </a>
                            </p>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="source-type-badge {% if source[4] == 1 %}source-status-active{% else %}source-status-inactive{% endif %}">
                                        {% if source[4] == 1 %}
                                        <i class="fas fa-check me-1"></i>نشط
                                        {% else %}
                                        <i class="fas fa-times me-1"></i>معطل
                                        {% endif %}
                                    </span>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ source[5] if source[5] else 'غير محدد' }}
                                    </small>
                                </div>
                                
                                <!-- نتيجة الاختبار -->
                                <div id="test-result-{{ source[0] }}" class="test-result" style="display: none;"></div>
                                
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-success btn-sm action-btn" onclick="testSource({{ source[0] }}, '{{ source[2] }}', '{{ source[3] }}')" title="اختبار المصدر">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <a href="/edit_source/{{ source[0] }}" class="btn btn-primary btn-sm action-btn" title="تعديل المصدر">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="/toggle_source/{{ source[0] }}" style="display: inline;">
                                        <button type="submit" class="btn btn-warning btn-sm action-btn" title="تغيير الحالة">
                                            <i class="fas fa-toggle-{% if source[4] == 1 %}on{% else %}off{% endif %}"></i>
                                        </button>
                                    </form>
                                    <a href="/delete_source/{{ source[0] }}" class="btn btn-danger btn-sm action-btn" title="حذف المصدر">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="fas fa-globe fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">لا توجد مصادر</h4>
                        <p class="text-muted mb-4">لم يتم العثور على مصادر للمعايير المحددة</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="/sources" class="btn btn-outline-primary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين الفلاتر
                            </a>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مصدر جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة مصدر -->
<div class="modal fade" id="addSourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مصدر جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/add_source">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                اسم المصدر
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="مثال: شفق نيوز">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">
                                <i class="fas fa-layer-group me-1"></i>
                                نوع المصدر
                            </label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر نوع المصدر</option>
                                <option value="website">
                                    <i class="fas fa-globe"></i> موقع ويب
                                </option>
                                <option value="facebook">
                                    <i class="fab fa-facebook"></i> فيسبوك
                                </option>
                                <option value="telegram">
                                    <i class="fab fa-telegram"></i> تلغرام
                                </option>
                                <option value="rss">
                                    <i class="fas fa-rss"></i> RSS Feed
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="url" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            رابط المصدر
                        </label>
                        <input type="url" class="form-control" id="url" name="url" required
                               placeholder="https://example.com">
                        <div class="form-text">
                            <small>
                                <strong>أمثلة:</strong><br>
                                • موقع ويب: https://shafaq.com<br>
                                • فيسبوك: https://www.facebook.com/pagename<br>
                                • تلغرام: https://t.me/channelname<br>
                                • RSS: https://example.com/rss.xml
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة المصدر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testSource(sourceId, url, type) {
    const resultDiv = document.getElementById(`test-result-${sourceId}`);
    const button = event.target.closest('button');

    // إظهار حالة التحميل
    resultDiv.className = 'test-result test-loading';
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاختبار...';
    button.disabled = true;

    fetch('/api/test-source', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            url: url,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        button.disabled = false;

        if (data.success) {
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <i class="fas fa-check-circle me-1"></i>
                نجح الاختبار!
                <br><small>العناوين: ${data.count || 0} | العراقية: ${data.iraq_related || 0}</small>
            `;
        } else {
            resultDiv.className = 'test-result test-error';
            resultDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-1"></i>
                فشل الاختبار
                <br><small>${data.message || 'خطأ غير معروف'}</small>
            `;
        }

        // إخفاء النتيجة بعد 10 ثوان
        setTimeout(() => {
            resultDiv.style.display = 'none';
        }, 10000);
    })
    .catch(error => {
        button.disabled = false;
        resultDiv.className = 'test-result test-error';
        resultDiv.innerHTML = `
            <i class="fas fa-times-circle me-1"></i>
            خطأ في الاتصال
            <br><small>${error.message}</small>
        `;

        setTimeout(() => {
            resultDiv.style.display = 'none';
        }, 10000);
    });
}

function testAllSources() {
    if (!confirm('هل تريد اختبار جميع المصادر النشطة؟\nقد يستغرق هذا بعض الوقت.')) {
        return;
    }

    const sourceCards = document.querySelectorAll('.source-card');
    let tested = 0;

    sourceCards.forEach((card, index) => {
        const testButton = card.querySelector('.btn-success');

        if (testButton) {
            setTimeout(() => {
                testButton.click();
                tested++;

                if (tested === sourceCards.length) {
                    setTimeout(() => {
                        alert(`تم الانتهاء من اختبار جميع المصادر!\nتم اختبار: ${tested} مصدر`);
                    }, 1000);
                }
            }, index * 2000); // تأخير 2 ثانية بين كل اختبار
        }
    });
}

// تحسينات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    document.querySelectorAll('.source-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#3498db';
            this.style.borderWidth = '2px';
        });

        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '';
            this.style.borderWidth = '';
        });
    });

    // تحسين نموذج إضافة المصدر
    const typeSelect = document.getElementById('type');
    const urlInput = document.getElementById('url');

    if (typeSelect && urlInput) {
        typeSelect.addEventListener('change', function() {
            const type = this.value;
            let placeholder = 'https://example.com';

            switch(type) {
                case 'website':
                    placeholder = 'https://shafaq.com';
                    break;
                case 'facebook':
                    placeholder = 'https://www.facebook.com/pagename';
                    break;
                case 'telegram':
                    placeholder = 'https://t.me/channelname';
                    break;
                case 'rss':
                    placeholder = 'https://example.com/rss.xml';
                    break;
            }

            urlInput.placeholder = placeholder;
        });
    }
});
</script>
{% endblock %}
