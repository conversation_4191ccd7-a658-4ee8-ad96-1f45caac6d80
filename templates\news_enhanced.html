{% extends "base.html" %}

{% block title %}الأخبار المحسنة - أداة الأخبار العراقية الذكية{% endblock %}

{% block extra_css %}
<style>
    .news-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        height: 100%;
    }
    .news-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .news-title {
        color: #2c3e50;
        text-decoration: none;
        font-size: 1rem;
        line-height: 1.4;
        display: block;
    }
    .news-title:hover {
        color: #3498db;
    }
    .source-badge {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 500;
    }
    .date-badge {
        background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8em;
    }
    .category-badge {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.7em;
        font-weight: 500;
    }
    .filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    .pagination-wrapper {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- إحصائيات سريعة -->
        <div class="stats-card">
            <div class="row text-center">
                <div class="col-md-3">
                    <h3 class="mb-1">{{ total_news }}</h3>
                    <small>إجمالي الأخبار</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ current_page }}</h3>
                    <small>الصفحة الحالية</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ total_pages }}</h3>
                    <small>إجمالي الصفحات</small>
                </div>
                <div class="col-md-3">
                    <h3 class="mb-1">{{ date_filter }}</h3>
                    <small>التاريخ المحدد</small>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filter-card p-4 mb-4">
            <h5 class="mb-3">
                <i class="fas fa-filter text-primary me-2"></i>
                تصفية وبحث الأخبار
            </h5>
            <form method="GET" action="/news">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">
                            <i class="fas fa-calendar me-1"></i>
                            التاريخ
                        </label>
                        <input type="date" class="form-control" name="date" value="{{ date_filter }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">
                            <i class="fas fa-globe me-1"></i>
                            المصدر
                        </label>
                        <select class="form-select" name="source">
                            <option value="">جميع المصادر</option>
                            {% for source in sources %}
                            <option value="{{ source[0] }}" {% if source_filter == source[0]|string %}selected{% endif %}>
                                {{ source[1] }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags me-1"></i>
                            الفئة
                        </label>
                        <select class="form-select" name="category">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                            <option value="{{ category[0] }}" {% if category_filter == category[0] %}selected{% endif %}>
                                {{ category[0] }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="/news" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="mb-4">
            <div class="btn-group" role="group">
                <button class="btn btn-success" onclick="fetchNewNews()">
                    <i class="fas fa-download me-1"></i>
                    جلب أخبار جديدة
                </button>
                <button class="btn btn-info" onclick="refreshNews()">
                    <i class="fas fa-sync me-1"></i>
                    تحديث الصفحة
                </button>
                <button class="btn btn-warning" onclick="cleanAllNews()">
                    <i class="fas fa-trash me-1"></i>
                    تنظيف الأخبار
                </button>
            </div>
        </div>

        <!-- الأخبار -->
        {% if news %}
            <div class="row">
                {% for item in news %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card news-card">
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title flex-grow-1 mb-3">
                                <a href="{{ item[3] }}" target="_blank" class="news-title">
                                    {{ item[0] }}
                                </a>
                            </h6>
                            
                            {% if item[5] %}
                            <p class="card-text text-muted small mb-3" style="font-size: 0.85em;">
                                {{ item[5][:120] }}{% if item[5]|length > 120 %}...{% endif %}
                            </p>
                            {% endif %}
                            
                            <div class="mt-auto">
                                {% if item[4] %}
                                <div class="mb-2">
                                    <span class="category-badge">{{ item[4] }}</span>
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="source-badge">{{ item[2] }}</span>
                                    <span class="date-badge">{{ item[1] }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- التصفح -->
            {% if total_pages > 1 %}
            <div class="pagination-wrapper mt-4">
                <nav aria-label="تصفح الأخبار">
                    <ul class="pagination justify-content-center mb-0">
                        {% if has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ current_page - 1 }}&date={{ date_filter }}&source={{ source_filter }}&category={{ category_filter }}">
                                <i class="fas fa-chevron-right"></i>
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_num }}&date={{ date_filter }}&source={{ source_filter }}&category={{ category_filter }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% elif page_num == 4 or page_num == total_pages - 3 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ current_page + 1 }}&date={{ date_filter }}&source={{ source_filter }}&category={{ category_filter }}">
                                التالي
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="fas fa-newspaper fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">لا توجد أخبار متاحة</h4>
                        <p class="text-muted mb-4">لم يتم العثور على أخبار للمعايير المحددة</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="/news" class="btn btn-outline-primary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين الفلاتر
                            </a>
                            <button class="btn btn-primary" onclick="fetchNewNews()">
                                <i class="fas fa-download me-1"></i>
                                جلب أخبار جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal التحميل -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري جلب الأخبار...</h5>
                <p class="text-muted">يرجى الانتظار بينما نجلب أحدث الأخبار العراقية</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshNews() {
    location.reload();
}

function fetchNewNews() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
    
    fetch('/api/fetch-news', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(function(data) {
        modal.hide();
        if (data.success) {
            // إظهار رسالة نجاح
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                تم جلب ${data.count || 0} خبر جديد بنجاح!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.row'));
            
            // تحديث الصفحة بعد ثانيتين
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('حدث خطأ: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        modal.hide();
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

function cleanAllNews() {
    if (!confirm('هل أنت متأكد أنك تريد حذف جميع الأخبار الحالية؟\nهذا الإجراء لا يمكن التراجع عنه.')) return;
    
    fetch('/api/clean-news', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حذف جميع الأخبار بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// تحديث تلقائي للوقت
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات تفاعلية للبطاقات
    document.querySelectorAll('.news-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#3498db';
        });
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '';
        });
    });
});
</script>
{% endblock %}
