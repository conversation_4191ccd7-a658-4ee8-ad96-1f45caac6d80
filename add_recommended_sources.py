#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإضافة المصادر العراقية الموصى بها تلقائياً
"""

import sqlite3
import sys
from datetime import datetime

# قائمة المصادر الموصى بها
RECOMMENDED_SOURCES = [
    # المواقع السريعة
    {
        'name': 'شفق نيوز',
        'url': 'https://shafaq.com',
        'type': 'website',
        'priority': 1
    },
    {
        'name': 'السومرية نيوز',
        'url': 'https://www.alsumaria.tv',
        'type': 'website',
        'priority': 1
    },
    {
        'name': 'وكالة الأنباء العراقية',
        'url': 'https://www.ina.iq',
        'type': 'website',
        'priority': 2
    },
    {
        'name': 'الزمان',
        'url': 'https://www.azzaman.com',
        'type': 'website',
        'priority': 2
    },
    {
        'name': 'بغداد اليوم',
        'url': 'https://baghdadtoday.news',
        'type': 'website',
        'priority': 3
    },
    
    # صفحات فيسبوك
    {
        'name': 'السومرية فيسبوك',
        'url': 'https://www.facebook.com/AlSumaria',
        'type': 'facebook',
        'priority': 1
    },
    {
        'name': 'شفق نيوز فيسبوك',
        'url': 'https://www.facebook.com/shafaqnews',
        'type': 'facebook',
        'priority': 1
    },
    {
        'name': 'رووداو فيسبوك',
        'url': 'https://www.facebook.com/rudawnews',
        'type': 'facebook',
        'priority': 2
    },
    {
        'name': 'العربية العراق فيسبوك',
        'url': 'https://www.facebook.com/AlArabiya.Iraq',
        'type': 'facebook',
        'priority': 2
    }
]

def add_sources_to_db(sources_list="all"):
    """إضافة المصادر إلى قاعدة البيانات"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('news_database.db')
        cursor = conn.cursor()
        
        # التأكد من وجود جدول المصادر
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                url TEXT NOT NULL UNIQUE,
                type TEXT NOT NULL,
                active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # تحديد المصادر المراد إضافتها
        if sources_list == "fast":
            # المصادر السريعة فقط (أولوية 1)
            sources_to_add = [s for s in RECOMMENDED_SOURCES if s['priority'] == 1]
        elif sources_list == "medium":
            # المصادر السريعة والمتوسطة (أولوية 1 و 2)
            sources_to_add = [s for s in RECOMMENDED_SOURCES if s['priority'] <= 2]
        else:
            # جميع المصادر
            sources_to_add = RECOMMENDED_SOURCES
        
        added_count = 0
        skipped_count = 0
        
        for source in sources_to_add:
            try:
                # التحقق من عدم وجود المصدر مسبقاً
                cursor.execute('SELECT id FROM sources WHERE url = ?', (source['url'],))
                if cursor.fetchone():
                    print(f"⚠️  المصدر موجود مسبقاً: {source['name']}")
                    skipped_count += 1
                    continue
                
                # إضافة المصدر
                cursor.execute('''
                    INSERT INTO sources (name, url, type, active, created_at)
                    VALUES (?, ?, ?, 1, ?)
                ''', (source['name'], source['url'], source['type'], datetime.now()))
                
                print(f"✅ تم إضافة: {source['name']} ({source['type']})")
                added_count += 1
                
            except sqlite3.IntegrityError:
                print(f"⚠️  خطأ في إضافة: {source['name']} (رابط مكرر)")
                skipped_count += 1
                continue
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print(f"\n📊 النتائج:")
        print(f"✅ تم إضافة {added_count} مصدر جديد")
        print(f"⚠️  تم تجاهل {skipped_count} مصدر موجود مسبقاً")
        print(f"📝 إجمالي المصادر المعالجة: {len(sources_to_add)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المصادر: {str(e)}")
        return False

def show_sources_info():
    """عرض معلومات المصادر الموصى بها"""
    
    print("📰 المصادر العراقية الموصى بها:")
    print("=" * 50)
    
    # تجميع حسب الأولوية
    priority_groups = {1: "سريعة", 2: "متوسطة", 3: "بطيئة"}
    
    for priority in [1, 2, 3]:
        sources = [s for s in RECOMMENDED_SOURCES if s['priority'] == priority]
        if sources:
            print(f"\n🚀 مصادر {priority_groups[priority]}:")
            for source in sources:
                print(f"  • {source['name']} ({source['type']})")
                print(f"    {source['url']}")

def main():
    """الدالة الرئيسية"""
    
    if len(sys.argv) < 2:
        print("🔧 استخدام السكريبت:")
        print("python add_recommended_sources.py [fast|medium|all|info]")
        print()
        print("الخيارات:")
        print("  fast   - إضافة المصادر السريعة فقط (5 مصادر)")
        print("  medium - إضافة المصادر السريعة والمتوسطة (7 مصادر)")
        print("  all    - إضافة جميع المصادر (9 مصادر)")
        print("  info   - عرض معلومات المصادر فقط")
        return
    
    command = sys.argv[1].lower()
    
    if command == "info":
        show_sources_info()
    elif command in ["fast", "medium", "all"]:
        print(f"🚀 بدء إضافة المصادر ({command})...")
        print()
        
        if add_sources_to_db(command):
            print("\n✅ تم الانتهاء بنجاح!")
            print("💡 يمكنك الآن تشغيل التطبيق واختبار المصادر الجديدة")
        else:
            print("\n❌ فشل في إضافة المصادر")
    else:
        print("❌ خيار غير صحيح. استخدم: fast, medium, all, أو info")

if __name__ == "__main__":
    main()
