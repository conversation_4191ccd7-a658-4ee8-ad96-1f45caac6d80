# بدائل فيسبوك للحصول على الأخبار العراقية

## 🚫 مشكلة فيسبوك الحالية

فيسبوك يحمي صفحاته من الـ Web Scraping بطرق متقدمة:
- **Cloudflare Protection** - حماية من البوتات
- **JavaScript Required** - المحتوى يحمل عبر JavaScript
- **Rate Limiting** - تحديد معدل الطلبات
- **User-Agent Detection** - كشف البوتات

## ✅ الحلول البديلة الموصى بها

### 1. مواقع الأخبار المباشرة (الأفضل)

#### مواقع سريعة وموثوقة:
```
✅ شفق نيوز - https://shafaq.com
✅ السومرية - https://www.alsumaria.tv
✅ وكالة الأنباء العراقية - https://www.ina.iq
✅ الزمان - https://www.azzaman.com
✅ بغداد اليوم - https://baghdadtoday.news
```

**المميزات:**
- 🚀 سرعة عالية
- 📊 محتوى منظم
- 🔄 تحديث مستمر
- 🛡️ لا توجد حماية من الـ scraping

### 2. مصادر RSS (إذا متوفرة)

بعض المواقع تدعم RSS:
```
✅ الجزيرة نت - https://www.aljazeera.net/rss/all
✅ العربية - https://www.alarabiya.net/rss.xml
```

### 3. قنوات تلغرام (بديل ممتاز)

قنوات تلغرام عامة للأخبار العراقية:
```
✅ شفق نيوز - https://t.me/shafaqnews
✅ السومرية - https://t.me/alsumaria
✅ وكالة الأنباء العراقية - https://t.me/ina_agency
```

**المميزات:**
- 📱 سهولة الوصول
- ⚡ تحديث فوري
- 📊 محتوى منظم
- 🔓 لا توجد قيود

## 🔧 إعداد البدائل

### إضافة مصادر مواقع الويب:

```python
# تشغيل سكريبت إضافة المصادر
python add_recommended_sources.py fast
```

### إضافة مصادر تلغرام (اختياري):

1. **احصل على API credentials من تلغرام:**
   - اذهب إلى https://my.telegram.org/apps
   - سجل تطبيق جديد
   - احصل على API ID و API Hash

2. **أضف المتغيرات في .env:**
   ```
   TELEGRAM_API_ID=your_api_id
   TELEGRAM_API_HASH=your_api_hash
   ```

3. **أضف قنوات تلغرام في التطبيق:**
   - النوع: telegram
   - الرابط: https://t.me/channel_name

## 📊 مقارنة الأداء

| المصدر | السرعة | الموثوقية | سهولة الإعداد | التحديث |
|---------|---------|-----------|----------------|----------|
| مواقع الويب | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| RSS | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| تلغرام | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| فيسبوك | ⭐⭐ | ⭐⭐ | ⭐ | ⭐⭐⭐ |

## 🎯 التوصية النهائية

### للاستخدام الفوري:
```
1. شفق نيوز - https://shafaq.com - website
2. السومرية - https://www.alsumaria.tv - website
3. وكالة الأنباء العراقية - https://www.ina.iq - website
```

### للاستخدام المتقدم:
```
أضف قنوات تلغرام بعد إعداد API credentials
```

## 🔄 خطة الانتقال

### الخطوة 1: إزالة مصادر فيسبوك المعطلة
```sql
-- في قاعدة البيانات
UPDATE sources SET active = 0 WHERE type = 'facebook';
```

### الخطوة 2: إضافة مصادر بديلة
```bash
python add_recommended_sources.py fast
```

### الخطوة 3: اختبار المصادر الجديدة
- اذهب إلى صفحة "المصادر"
- اختبر كل مصدر جديد
- تأكد من جودة النتائج

### الخطوة 4: مراقبة الأداء
- راقب السجلات لمدة يوم
- تحقق من جودة الأخبار المجمعة
- أضف مصادر إضافية حسب الحاجة

## 💡 نصائح للحصول على أفضل النتائج

### 1. تنويع المصادر
- استخدم 3-5 مصادر مختلفة
- امزج بين المواقع الرسمية والإعلامية
- أضف مصادر محلية للمحافظات

### 2. مراقبة الجودة
- راجع الأخبار المجمعة يومياً
- احذف المصادر ذات الجودة المنخفضة
- أضف مصادر جديدة بانتظام

### 3. تحسين الأداء
- ابدأ بمصادر قليلة وسريعة
- زد المصادر تدريجياً
- راقب أوقات الاستجابة

## 🎉 النتيجة المتوقعة

بعد تطبيق هذه البدائل:
- ✅ **سرعة أعلى** - جلب أسرع للأخبار
- ✅ **موثوقية أكبر** - لا توجد أخطاء اتصال
- ✅ **محتوى أكثر** - مصادر متنوعة
- ✅ **استقرار أفضل** - لا توجد قيود من فيسبوك
