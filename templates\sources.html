{% extends "base.html" %}

{% block title %}مصادر الأخبار - أداة الأخبار العراقية الذكية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-rss me-2"></i>
                    إدارة مصادر الأخبار
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted">إضافة وإدارة مصادر الأخبار العراقية النصية من مواقع مختلفة</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> يتم جلب النصوص فقط من المصادر (العناوين والمحتوى النصي) بدون صور أو ملفات مرفقة
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مصدر جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_source') }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم المصدر</label>
                        <input type="text" class="form-control" id="name" name="name" required
                               placeholder="مثال: موقع الأخبار العراقي">
                    </div>

                    <div class="mb-3">
                        <label for="url" class="form-label">رابط المصدر</label>
                        <input type="url" class="form-control" id="url" name="url" required
                               placeholder="https://example.com/rss أو https://example.com">
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">نوع المصدر</label>
                        <select class="form-control" id="type" name="type" required>
                            <option value="">اختر نوع المصدر</option>
                            <option value="rss">RSS Feed</option>
                            <option value="website">موقع ويب</option>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة المصدر
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>استخدم RSS Feed للحصول على أفضل النتائج النصية</li>
                        <li>تأكد من أن المصدر يحتوي على أخبار عراقية نصية</li>
                        <li>يتم جلب النصوص فقط (بدون صور أو فيديوهات)</li>
                        <li>يتم تصفية الأخبار تلقائياً حسب المعايير المحددة</li>
                        <li>يمكن إضافة مصادر متعددة لتغطية أوسع</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-filter me-2"></i>معايير التصفية:</h6>
                    <ul class="mb-0">
                        <li>أخبار العراق فقط (بدون كردستان)</li>
                        <li>استبعاد أخبار الفن والرياضة والطقس</li>
                        <li>عرض أخبار اليوم الحالي فقط</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    المصادر المضافة ({{ sources|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if sources %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المصدر</th>
                                    <th>النوع</th>
                                    <th>الرابط</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for source in sources %}
                                <tr>
                                    <td>
                                        <strong>{{ source[1] }}</strong>
                                    </td>
                                    <td>
                                        {% if source[3] == 'rss' %}
                                            <span class="badge bg-primary">RSS Feed</span>
                                        {% else %}
                                            <span class="badge bg-secondary">موقع ويب</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ source[2] }}" target="_blank" class="text-decoration-none">
                                            {{ source[2][:50] }}{% if source[2]|length > 50 %}...{% endif %}
                                            <i class="fas fa-external-link-alt ms-1"></i>
                                        </a>
                                    </td>
                                    <td>
                                        {% if source[4] %}
                                            <span class="badge bg-success">مفعل</span>
                                        {% else %}
                                            <span class="badge bg-danger">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ source[5][:10] if source[5] else 'غير محدد' }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info"
                                                    onclick="testSource({{ source[0] }}, '{{ source[2] }}', '{{ source[3] }}')">
                                                <i class="fas fa-test"></i>
                                            </button>
                                            <a href="{{ url_for('delete_source', source_id=source[0]) }}"
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المصدر؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مصادر مضافة</h5>
                        <p class="text-muted">قم بإضافة مصدر أخبار جديد للبدء</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- مصادر مقترحة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    مصادر أخبار عراقية مقترحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">وكالة الأنباء العراقية</h6>
                                <p class="card-text small">المصدر الرسمي للأخبار العراقية</p>
                                <button class="btn btn-sm btn-primary" onclick="addSuggestedSource('وكالة الأنباء العراقية', 'https://ina.iq/rss', 'rss')">
                                    <i class="fas fa-plus me-1"></i>إضافة
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body">
                                <h6 class="card-title">شبكة الإعلام العراقي</h6>
                                <p class="card-text small">الشبكة الإعلامية الرسمية</p>
                                <button class="btn btn-sm btn-success" onclick="addSuggestedSource('شبكة الإعلام العراقي', 'https://imn.iq', 'website')">
                                    <i class="fas fa-plus me-1"></i>إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للاختبار -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختبار المصدر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testResult">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الاختبار...</span>
                        </div>
                        <p class="mt-2">جاري اختبار المصدر...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function addSuggestedSource(name, url, type) {
    document.getElementById('name').value = name;
    document.getElementById('url').value = url;
    document.getElementById('type').value = type;

    // التمرير إلى النموذج
    document.getElementById('name').scrollIntoView({ behavior: 'smooth' });
    document.getElementById('name').focus();
}

function testSource(sourceId, url, type) {
    const modal = new bootstrap.Modal(document.getElementById('testModal'));
    modal.show();

    fetch('/api/test-source', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            url: url,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('testResult');
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>نجح الاختبار!</strong><br>
                    تم العثور على ${data.count} عنصر في المصدر
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>فشل الاختبار!</strong><br>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('testResult').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>خطأ في الاتصال!</strong><br>
                تعذر اختبار المصدر
            </div>
        `;
    });
}
</script>
{% endblock %}
