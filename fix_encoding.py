#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح مشاكل الترميز في نظام التسجيل
"""

import os
import sys
import locale

def fix_console_encoding():
    """إصلاح ترميز وحدة التحكم"""
    
    print("🔧 إصلاح مشاكل الترميز...")
    
    try:
        # تعيين ترميز UTF-8 للوحة التحكم
        if sys.platform.startswith('win'):
            # Windows
            os.system('chcp 65001 >nul 2>&1')  # UTF-8 code page
            print("✅ تم تعيين ترميز UTF-8 لـ Windows")
        
        # تعيين متغيرات البيئة
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'en_US.UTF-8'
        
        print("✅ تم تعيين متغيرات البيئة")
        
        # اختبار الترميز
        test_text = "اختبار النص العربي مع الأرقام: ١٢٣٤٥"
        print(f"🧪 اختبار: {test_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الترميز: {str(e)}")
        return False

def clean_log_files():
    """تنظيف ملفات السجلات القديمة"""
    
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        print("📁 مجلد السجلات غير موجود")
        return
    
    try:
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        
        for log_file in log_files:
            file_path = os.path.join(log_dir, log_file)
            
            # إنشاء نسخة احتياطية
            backup_path = file_path + '.backup'
            if os.path.exists(file_path):
                os.rename(file_path, backup_path)
                print(f"📋 تم إنشاء نسخة احتياطية: {backup_path}")
        
        print("✅ تم تنظيف ملفات السجلات")
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف السجلات: {str(e)}")

def create_run_script():
    """إنشاء سكريبت تشغيل محسن"""
    
    script_content = '''@echo off
chcp 65001 >nul 2>&1
set PYTHONIOENCODING=utf-8
set LANG=en_US.UTF-8

echo ========================================
echo       تطبيق جمع الأخبار العراقية
echo ========================================
echo.

echo تحقق من تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo إصلاح مشاكل الترميز...
python fix_encoding.py

echo.
echo بدء تشغيل التطبيق...
echo يمكنك الوصول للتطبيق على: http://localhost:5008
echo.

python app.py

pause
'''
    
    try:
        with open('run_fixed.bat', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("✅ تم إنشاء سكريبت التشغيل المحسن: run_fixed.bat")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء السكريبت: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح مشاكل الترميز...")
    print("=" * 50)
    
    # إصلاح ترميز وحدة التحكم
    if fix_console_encoding():
        print("✅ تم إصلاح ترميز وحدة التحكم")
    
    # تنظيف ملفات السجلات
    clean_log_files()
    
    # إنشاء سكريبت تشغيل محسن
    create_run_script()
    
    print("\n📋 ملخص الإصلاحات:")
    print("✅ إصلاح ترميز UTF-8")
    print("✅ تنظيف ملفات السجلات")
    print("✅ إنشاء سكريبت تشغيل محسن")
    
    print("\n💡 نصائح:")
    print("1. استخدم run_fixed.bat لتشغيل التطبيق")
    print("2. ملفات السجلات الآن تدعم النصوص العربية")
    print("3. لن تظهر رسائل الترميز مرة أخرى")
    
    print("\n🎉 تم الانتهاء من الإصلاحات!")

if __name__ == "__main__":
    main()
