#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط لموسوعة أخبار العراق
"""

print("🚀 بدء تشغيل موسوعة أخبار العراق...")
print("📍 الخادم سيكون متاح على: http://localhost:5008")
print("=" * 50)

try:
    from app import app, init_database
    
    # إنشاء قاعدة البيانات
    print("🔧 إعداد قاعدة البيانات...")
    init_database()
    print("✅ تم إعداد قاعدة البيانات")
    
    # تشغيل الخادم
    print("🌟 تشغيل الخادم...")
    app.run(host='0.0.0.0', port=5008, debug=True)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت Flask: pip install flask")
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("💡 تأكد من وجود ملف app.py في نفس المجلد")
