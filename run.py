#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل موسوعة أخبار العراق
"""

import os
import sys
import subprocess
import time

def install_requirements():
    """تثبيت المتطلبات"""
    print("🔧 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_requirements():
    """فحص المتطلبات"""
    required_packages = [
        'flask', 'requests', 'beautifulsoup4', 'selenium', 
        'fake-useragent', 'cloudscraper', 'feedparser'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل موسوعة أخبار العراق")
    print("=" * 50)
    
    # فحص المتطلبات
    missing = check_requirements()
    if missing:
        print(f"⚠️  المتطلبات المفقودة: {', '.join(missing)}")
        if input("هل تريد تثبيت المتطلبات؟ (y/n): ").lower() == 'y':
            if not install_requirements():
                print("❌ فشل في تثبيت المتطلبات. يرجى تثبيتها يدوياً.")
                return
        else:
            print("❌ لا يمكن تشغيل النظام بدون المتطلبات.")
            return
    
    # تشغيل التطبيق
    print("\n🌟 تشغيل موسوعة أخبار العراق...")
    print("📍 الخادم متاح على: http://localhost:5008")
    print("🔄 اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        from app import app
        app.run(host='0.0.0.0', port=5008, debug=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    main()
