#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل موسوعة أخبار العراق
"""

import os
import sys

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل موسوعة أخبار العراق")
    print("=" * 50)

    # تشغيل التطبيق
    print("🌟 تشغيل موسوعة أخبار العراق...")
    print("📍 الخادم متاح على: http://localhost:5008")
    print("🔄 اضغط Ctrl+C للإيقاف")
    print("=" * 50)

    try:
        # استيراد وتشغيل التطبيق
        import app
        if hasattr(app, 'app'):
            app.app.run(host='0.0.0.0', port=5008, debug=True)
        else:
            print("❌ خطأ: لم يتم العثور على التطبيق")
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install flask")
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print(f"📝 تفاصيل الخطأ: {type(e).__name__}")

if __name__ == '__main__':
    main()
