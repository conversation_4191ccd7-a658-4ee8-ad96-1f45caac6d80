# تطبيق جمع الأخبار العراقية

تطبيق ذكي لجمع وتصفية الأخبار العراقية من مصادر متعددة بشكل تلقائي.

## المميزات الرئيسية

✅ **جمع تلقائي** - يجلب الأخبار كل 30 دقيقة  
✅ **مصادر متعددة** - RSS, مواقع ويب, فيسبوك, تلغرام  
✅ **تصفية ذكية** - يركز على الأخبار العراقية فقط  
✅ **معالجة متوازية** - سرعة في جلب البيانات  
✅ **واجهة سهلة** - إدارة المصادر والأخبار  

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python app.py
```

أو استخدم الملف المساعد:
```bash
run.bat
```

### 3. الوصول للتطبيق
افتح المتصفح واذهب إلى: `http://localhost:5008`

## أنواع المصادر المدعومة

### 1. RSS Feeds
- مصادر الأخبار التقليدية
- سريع وموثوق
- مثال: `https://www.aljazeera.net/rss/all`

### 2. مواقع الويب
- المواقع التي لا تدعم RSS
- يحلل صفحات HTML
- مثال: `https://www.iraqinews.com`

### 3. فيسبوك (Web Scraping)
- **لا يحتاج API Token**
- يعمل مع الصفحات العامة
- مثال: `https://www.facebook.com/IraqiNews`

### 4. تلغرام (اختياري)
- يحتاج إعداد API
- للقنوات العامة
- مثال: `https://t.me/iraqnews`

## إضافة مصادر جديدة

1. اذهب إلى صفحة "المصادر"
2. انقر "إضافة مصدر جديد"
3. أدخل البيانات:
   - **الاسم**: اسم المصدر
   - **الرابط**: URL المصدر
   - **النوع**: نوع المصدر
4. انقر "اختبار المصدر" للتأكد
5. احفظ المصدر

## مراقبة النظام

### السجلات
- تحفظ في مجلد `logs/`
- تتضمن تفاصيل العمليات والأخطاء
- تدور تلقائياً لتوفير المساحة

### الإحصائيات
- عدد المصادر النشطة
- عدد الأخبار اليومية
- إحصائيات الأداء

## استكشاف الأخطاء

### مشاكل شائعة:

**"لا توجد أخبار"**
- تحقق من المصادر النشطة
- راجع السجلات للأخطاء
- اختبر المصادر يدوياً

**"خطأ في الاتصال"**
- تحقق من الاتصال بالإنترنت
- بعض المواقع قد تحظر الطلبات
- جرب تغيير User-Agent

**"بطء في التحميل"**
- قلل عدد المصادر
- زد فترة الجدولة
- تحقق من أداء الخادم

## الإعدادات المتقدمة

### متغيرات البيئة (اختيارية)
انسخ `.env.example` إلى `.env` وعدل القيم:

```bash
# للتلغرام فقط
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# إعدادات التطبيق
AUTO_FETCH_INTERVAL=30
MAX_WORKERS=4
```

### تخصيص الكلمات المفتاحية
عدل `IRAQ_KEYWORDS` في `app.py` لإضافة كلمات جديدة.

## الأمان

- لا تشارك ملفات `.env`
- استخدم HTTPS في الإنتاج
- راقب السجلات للأنشطة المشبوهة

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، راجع:
- ملف `FACEBOOK_SETUP.md` لإعداد فيسبوك
- السجلات في مجلد `logs/`
- اختبار المصادر من الواجهة
