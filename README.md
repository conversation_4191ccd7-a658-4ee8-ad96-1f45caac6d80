# 🇮🇶 موسوعة أخبار العراق

## نظام متطور لجلب وعرض الأخبار العراقية من مصادر متعددة

### 🌟 **المميزات الرئيسية**

- 🌐 **جلب من مواقع الويب**: استخراج ذكي للأخبار من المواقع الإخبارية
- 📘 **دعم فيسبوك**: جلب المنشورات من صفحات فيسبوك الإخبارية  
- 📡 **دعم RSS**: قراءة تغذية RSS للمواقع المدعومة
- 🤖 **تقنيات متقدمة**: Selenium, CloudS<PERSON>raper, تجاوز الحماية
- 🎯 **تصفية ذكية**: فلترة الأخبار العراقية تلقائياً
- 📊 **تصنيف تلقائي**: تصنيف الأخبار حسب الموضوع
- 🎨 **تصميم عصري**: واجهة مستوحاة من 964media
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة

---

## 🚀 **التثبيت والتشغيل**

### **الطريقة السريعة:**
```bash
python run.py
```

### **التثبيت اليدوي:**
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل التطبيق
python app.py
```

### **الوصول للنظام:**
```
🌐 الرابط: http://localhost:5008
📱 الشبكة المحلية: http://[your-ip]:5008
```

---

## 📁 **هيكل المشروع**

```
iraq-news-encyclopedia/
├── 📄 app.py                 # التطبيق الرئيسي
├── 🔧 news_fetcher.py        # محرك جلب الأخبار
├── 🚀 run.py                 # ملف التشغيل السريع
├── 📋 requirements.txt       # المتطلبات
├── 🗄️ iraq_news.db          # قاعدة البيانات
├── 📁 templates/             # قوالب HTML
│   ├── base.html            # القالب الأساسي
│   ├── index.html           # الصفحة الرئيسية
│   ├── news.html            # صفحة الأخبار
│   ├── source_news.html     # أخبار المصدر
│   ├── admin.html           # لوحة الإدارة
│   └── add_source.html      # إضافة مصدر
├── 📁 logs/                 # ملفات السجلات
└── 📖 README.md             # هذا الملف
```

---

## 🎯 **الاستخدام**

### **1. الصفحة الرئيسية**
- عرض جميع المصادر النشطة
- إحصائيات سريعة
- بحث في المصادر
- جلب أخبار جديدة

### **2. إدارة المصادر**
- إضافة مصادر جديدة
- تفعيل/إلغاء تفعيل المصادر
- مراقبة أداء المصادر
- إحصائيات مفصلة

### **3. عرض الأخبار**
- تصفية حسب المصدر والتاريخ والفئة
- تصفح بالصفحات
- روابط مباشرة للأخبار الأصلية

---

## 🔧 **التقنيات المستخدمة**

### **Backend:**
- **Flask**: إطار العمل الرئيسي
- **SQLite**: قاعدة البيانات
- **BeautifulSoup**: تحليل HTML
- **Selenium**: للمواقع المعقدة
- **CloudScraper**: تجاوز Cloudflare
- **Requests**: طلبات HTTP
- **FeedParser**: قراءة RSS

### **Frontend:**
- **Bootstrap 5**: التصميم المتجاوب
- **Font Awesome**: الأيقونات
- **Google Fonts**: الخطوط العربية
- **JavaScript**: التفاعل

### **AI & NLP:**
- **LangDetect**: كشف اللغة
- **TextBlob**: معالجة النصوص
- **NLTK**: معالجة اللغة الطبيعية

---

## 📊 **المصادر المدعومة**

### **مواقع الويب:**
- شفق نيوز (shafaq.com)
- السومرية (alsumaria.tv)
- الفرات نيوز (alforatnews.iq)
- واع (ina.iq)
- المعلومة (almaalomah.me)
- بغداد اليوم (baghdadtoday.news)
- الصباح (alsabaah.iq)
- دجلة (dijlah.tv)

### **وسائل التواصل:**
- صفحات فيسبوك الإخبارية
- قنوات تلغرام (قريباً)

### **RSS Feeds:**
- جميع المواقع المدعومة لـ RSS

---

## ⚙️ **الإعدادات المتقدمة**

### **إعدادات قاعدة البيانات:**
```python
# في app.py
DATABASE_PATH = 'iraq_news.db'
CONNECTION_TIMEOUT = 30
```

### **إعدادات جلب الأخبار:**
```python
# في news_fetcher.py
MAX_NEWS_PER_SOURCE = 20
FETCH_TIMEOUT = 30
RETRY_ATTEMPTS = 3
```

### **كلمات مفتاحية للفلترة:**
```python
IRAQ_KEYWORDS = [
    'العراق', 'بغداد', 'البصرة', 'الموصل', 
    'أربيل', 'النجف', 'كربلاء', 'الأنبار'
    # ... المزيد
]
```

---

## 🛠️ **التطوير والتخصيص**

### **إضافة مصدر جديد:**
1. اذهب إلى لوحة الإدارة
2. اضغط "إضافة مصدر جديد"
3. املأ البيانات المطلوبة
4. اختر نوع المصدر (موقع/فيسبوك/RSS)
5. احفظ المصدر

### **تخصيص التصميم:**
```css
/* في templates/base.html */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    /* ... المزيد */
}
```

### **إضافة فئة جديدة:**
```python
# في news_fetcher.py
categories = {
    'فئة_جديدة': ['كلمة1', 'كلمة2', 'كلمة3'],
    # ... المزيد
}
```

---

## 📈 **الأداء والمراقبة**

### **السجلات:**
- `logs/app.log`: سجل التطبيق الرئيسي
- مراقبة عمليات الجلب
- تتبع الأخطاء والتحذيرات

### **الإحصائيات:**
- عدد المصادر النشطة
- إجمالي الأخبار المجلوبة
- أخبار اليوم الحالي
- معدل نجاح الجلب

### **التحسين:**
- تخزين مؤقت للصفحات
- تأخير بين طلبات الجلب
- إعادة المحاولة عند الفشل
- تنظيف البيانات القديمة

---

## 🔒 **الأمان والخصوصية**

### **حماية البيانات:**
- تشفير كلمات المرور (إن وجدت)
- تنظيف مدخلات المستخدم
- حماية من SQL Injection
- تحديد معدل الطلبات

### **احترام المواقع:**
- تأخير بين الطلبات
- استخدام User-Agent مناسب
- احترام robots.txt
- عدم إرهاق الخوادم

---

## 🐛 **استكشاف الأخطاء**

### **مشاكل شائعة:**

**1. خطأ في تثبيت المتطلبات:**
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

**2. مشكلة في Selenium:**
```bash
# تثبيت Chrome Driver
pip install webdriver-manager
```

**3. خطأ في قاعدة البيانات:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm iraq_news.db
python app.py
```

**4. مشكلة في الترميز:**
```bash
# تأكد من دعم UTF-8
export PYTHONIOENCODING=utf-8
```

---

## 🤝 **المساهمة**

### **كيفية المساهمة:**
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. تطوير الميزة مع الاختبارات
4. إرسال Pull Request

### **مجالات المساهمة:**
- إضافة مصادر جديدة
- تحسين خوارزميات الاستخراج
- تطوير واجهة المستخدم
- إضافة ميزات جديدة
- تحسين الأداء
- ترجمة الواجهة

---

## 📞 **الدعم والتواصل**

### **للمساعدة:**
- إنشاء Issue في GitHub
- مراجعة الوثائق
- فحص ملفات السجلات

### **للتطوير:**
- مراجعة الكود المصدري
- اتباع معايير Python PEP8
- كتابة اختبارات للميزات الجديدة

---

## 📄 **الترخيص**

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

## 🎉 **شكر خاص**

- **964media.com** للإلهام في التصميم
- مجتمع المطورين العرب
- جميع المساهمين في المشروع

---

**🇮🇶 صُنع بحب للعراق 🇮🇶**
