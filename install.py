#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تثبيت المتطلبات لموسوعة أخبار العراق
"""

import subprocess
import sys

def install_package(package):
    """تثبيت حزمة واحدة"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🔧 تثبيت متطلبات موسوعة أخبار العراق...")
    print("=" * 50)
    
    # المتطلبات الأساسية
    packages = [
        'Flask',
        'requests', 
        'beautifulsoup4',
        'lxml'
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"📦 تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package} بنجاح")
            success_count += 1
        else:
            print(f"❌ فشل في تثبيت {package}")
    
    print("=" * 50)
    print(f"📊 تم تثبيت {success_count} من {len(packages)} حزمة")
    
    if success_count == len(packages):
        print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام بـ: python start.py")
    else:
        print("⚠️ بعض الحزم لم يتم تثبيتها. جرب تثبيتها يدوياً:")
        for package in packages:
            print(f"   pip install {package}")

if __name__ == '__main__':
    main()
