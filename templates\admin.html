{% extends "base.html" %}

{% block title %}لوحة الإدارة - موسوعة أخبار العراق{% endblock %}

{% block extra_css %}
<style>
    .admin-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 30px 0;
        border-radius: 20px;
        margin-bottom: 30px;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        text-align: center;
        transition: all 0.3s ease;
        border-left: 4px solid var(--secondary-color);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }

    .stat-icon {
        font-size: 2rem;
        color: var(--secondary-color);
        margin-bottom: 15px;
    }

    .admin-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .section-title {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--border-color);
    }

    .action-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 15px;
        border-left: 4px solid var(--secondary-color);
    }

    .action-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .action-description {
        color: #6c757d;
        margin-bottom: 15px;
        font-size: 0.9rem;
    }

    .recent-activity {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item {
        padding: 15px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--secondary-color);
        color: white;
        font-size: 0.9rem;
    }

    .activity-content {
        flex-grow: 1;
    }

    .activity-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 5px;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
        
        .admin-section {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Admin Header -->
<div class="admin-header">
    <h1 class="mb-3">
        <i class="fas fa-cogs me-3"></i>
        لوحة الإدارة
    </h1>
    <p class="mb-0 opacity-75">إدارة مصادر الأخبار ومراقبة النظام</p>
</div>

<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-globe"></i>
        </div>
        <div class="stat-number">{{ total_sources }}</div>
        <div class="stat-label">إجمالي المصادر</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ active_sources }}</div>
        <div class="stat-label">مصادر نشطة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-newspaper"></i>
        </div>
        <div class="stat-number">{{ total_news }}</div>
        <div class="stat-label">إجمالي الأخبار</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stat-number">{{ today_news }}</div>
        <div class="stat-label">أخبار اليوم</div>
    </div>
</div>

<!-- Quick Actions -->
<div class="admin-section">
    <h3 class="section-title">
        <i class="fas fa-bolt me-2"></i>
        إجراءات سريعة
    </h3>
    
    <div class="quick-actions">
        <div class="action-card">
            <div class="action-title">
                <i class="fas fa-plus me-2 text-success"></i>
                إضافة مصدر جديد
            </div>
            <div class="action-description">
                إضافة مصدر أخبار جديد للنظام
            </div>
            <a href="{{ url_for('add_source') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                إضافة مصدر
            </a>
        </div>
        
        <div class="action-card">
            <div class="action-title">
                <i class="fas fa-sync-alt me-2 text-primary"></i>
                جلب أخبار جديدة
            </div>
            <div class="action-description">
                جلب أخبار من جميع المصادر النشطة
            </div>
            <button class="btn btn-primary" onclick="fetchAllNews()">
                <i class="fas fa-sync-alt me-2"></i>
                جلب الأخبار
            </button>
        </div>
        
        <div class="action-card">
            <div class="action-title">
                <i class="fas fa-list me-2 text-info"></i>
                عرض جميع الأخبار
            </div>
            <div class="action-description">
                استعراض وإدارة جميع الأخبار
            </div>
            <a href="{{ url_for('all_news') }}" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>
                عرض الأخبار
            </a>
        </div>
        
        <div class="action-card">
            <div class="action-title">
                <i class="fas fa-chart-bar me-2 text-warning"></i>
                تقارير وإحصائيات
            </div>
            <div class="action-description">
                عرض تقارير مفصلة عن الأداء
            </div>
            <button class="btn btn-warning" onclick="showReports()">
                <i class="fas fa-chart-bar me-2"></i>
                عرض التقارير
            </button>
        </div>
    </div>
</div>

<!-- Sources Management -->
<div class="admin-section">
    <h3 class="section-title">
        <i class="fas fa-cog me-2"></i>
        إدارة المصادر
    </h3>
    
    <div class="table-responsive">
        <table class="table table-hover">
            <thead class="table-dark">
                <tr>
                    <th>المصدر</th>
                    <th>النوع</th>
                    <th>الفئة</th>
                    <th>الحالة</th>
                    <th>آخر جلب</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody id="sourcesTable">
                <!-- سيتم تحميل المصادر هنا -->
                <tr>
                    <td colspan="6" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Recent Activity -->
<div class="admin-section">
    <h3 class="section-title">
        <i class="fas fa-history me-2"></i>
        النشاط الأخير
    </h3>
    
    <div class="recent-activity">
        {% if recent_fetches %}
            {% for fetch in recent_fetches %}
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">جلب أخبار من {{ fetch[0] }}</div>
                    <div class="activity-time">
                        <i class="fas fa-clock me-1"></i>
                        {{ fetch[1] if fetch[1] else 'غير محدد' }}
                        <span class="ms-3">
                            <i class="fas fa-newspaper me-1"></i>
                            {{ fetch[2] }} محاولة
                        </span>
                        <span class="ms-3">
                            <i class="fas fa-check me-1"></i>
                            {{ fetch[3] }} نجح
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>لا يوجد نشاط حديث</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                <h5 id="loadingText">جاري المعالجة...</h5>
                <p class="text-muted mb-0" id="loadingDetails">يرجى الانتظار</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل المصادر
function loadSources() {
    fetch('/api/sources')
    .then(response => response.json())
    .then(data => {
        const tbody = document.getElementById('sourcesTable');
        tbody.innerHTML = '';
        
        if (data.sources && data.sources.length > 0) {
            data.sources.forEach(source => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            ${source.icon_url ? 
                                `<img src="${source.icon_url}" alt="${source.name}" class="me-2" style="width: 24px; height: 24px; border-radius: 4px;">` :
                                `<div class="me-2 bg-primary text-white d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; border-radius: 4px; font-size: 0.8rem;">${source.name[0]}</div>`
                            }
                            <div>
                                <div class="fw-bold">${source.name}</div>
                                <small class="text-muted">${source.url.substring(0, 30)}...</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-info">${source.type}</span></td>
                    <td>${source.category}</td>
                    <td>
                        <span class="badge ${source.active ? 'bg-success' : 'bg-secondary'}">
                            ${source.active ? 'نشط' : 'معطل'}
                        </span>
                    </td>
                    <td>${source.last_fetch || 'لم يتم الجلب'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="testSource(${source.id})">
                                <i class="fas fa-vial"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="toggleSource(${source.id})">
                                <i class="fas fa-toggle-${source.active ? 'on' : 'off'}"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSource(${source.id}, '${source.name}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        } else {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد مصادر</p>
                    </td>
                </tr>
            `;
        }
    })
    .catch(error => {
        console.error('خطأ في تحميل المصادر:', error);
        document.getElementById('sourcesTable').innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>خطأ في تحميل المصادر</p>
                </td>
            </tr>
        `;
    });
}

// جلب جميع الأخبار
function fetchAllNews() {
    if (!confirm('هل تريد جلب أخبار جديدة من جميع المصادر النشطة؟\nقد يستغرق هذا عدة دقائق.')) {
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
    
    document.getElementById('loadingText').textContent = 'جاري جلب الأخبار...';
    document.getElementById('loadingDetails').textContent = 'قد يستغرق هذا عدة دقائق';
    
    fetch('/api/fetch-news', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        modal.hide();
        
        if (data.success) {
            alert('تم بدء عملية جلب الأخبار بنجاح!\nسيتم تحديث الصفحة تلقائياً.');
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        modal.hide();
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// اختبار مصدر
function testSource(sourceId) {
    alert('ميزة اختبار المصدر ستكون متاحة قريباً');
}

// تفعيل/إلغاء تفعيل مصدر
function toggleSource(sourceId) {
    if (!confirm('هل تريد تغيير حالة هذا المصدر؟')) {
        return;
    }
    
    fetch(`/toggle_source/${sourceId}`, {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            loadSources(); // إعادة تحميل المصادر
            alert('تم تغيير حالة المصدر بنجاح');
        } else {
            alert('حدث خطأ في تغيير حالة المصدر');
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// حذف مصدر
function deleteSource(sourceId, sourceName) {
    if (!confirm(`هل أنت متأكد من حذف المصدر "${sourceName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }
    
    fetch(`/delete_source/${sourceId}`, {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            loadSources(); // إعادة تحميل المصادر
            alert('تم حذف المصدر بنجاح');
        } else {
            alert('حدث خطأ في حذف المصدر');
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// عرض التقارير
function showReports() {
    alert('ميزة التقارير ستكون متاحة قريباً');
}

// تحميل المصادر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // loadSources(); // تعطيل مؤقت حتى إنشاء API
    
    // محاكاة تحميل المصادر
    setTimeout(() => {
        document.getElementById('sourcesTable').innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>استخدم "إضافة مصدر" لبدء إضافة مصادر الأخبار</p>
                </td>
            </tr>
        `;
    }, 1000);
});
</script>
{% endblock %}
