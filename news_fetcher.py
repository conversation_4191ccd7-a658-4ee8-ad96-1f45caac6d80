#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة جلب الأخبار المتقدمة
تدعم جلب الأخبار من مواقع الويب وفيسبوك بتقنيات متطورة
"""

import requests
import sqlite3
import logging
import time
import random
from datetime import datetime, timed<PERSON>ta
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
from fake_useragent import UserAgent
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import feedparser
from langdetect import detect
import nltk
from textblob import TextBlob

# إعداد السجلات
logger = logging.getLogger(__name__)

# إعداد User Agent
ua = UserAgent()

# كلمات مفتاحية للأخبار العراقية
IRAQ_KEYWORDS = [
    'العراق', 'بغداد', 'البصرة', 'الموصل', 'أربيل', 'النجف', 'كربلاء', 'الأنبار',
    'ديالى', 'صلاح الدين', 'كركوك', 'ميسان', 'ذي قار', 'المثنى', 'واسط',
    'السليمانية', 'دهوك', 'حلبجة', 'العمارة', 'الناصرية', 'الرمادي',
    'تكريت', 'سامراء', 'الفلوجة', 'الحلة', 'الكوت', 'عراقي', 'عراقية'
]

class NewsScraperEngine:
    """محرك جلب الأخبار المتقدم"""
    
    def __init__(self):
        self.session = requests.Session()
        self.scraper = cloudscraper.create_scraper()
        self.driver = None
        self.setup_session()
    
    def setup_session(self):
        """إعداد جلسة الطلبات"""
        headers = {
            'User-Agent': ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def get_selenium_driver(self):
        """إنشاء متصفح Selenium للمواقع المعقدة"""
        if self.driver is None:
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument(f'--user-agent={ua.random}')
            
            try:
                self.driver = webdriver.Chrome(
                    service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                    options=options
                )
            except Exception as e:
                logger.error(f"فشل في إنشاء متصفح Selenium: {e}")
                return None
        
        return self.driver
    
    def fetch_with_requests(self, url, timeout=30):
        """جلب المحتوى باستخدام requests"""
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.error(f"فشل جلب {url} بـ requests: {e}")
            return None
    
    def fetch_with_cloudscraper(self, url, timeout=30):
        """جلب المحتوى باستخدام cloudscraper (لتجاوز Cloudflare)"""
        try:
            response = self.scraper.get(url, timeout=timeout)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.error(f"فشل جلب {url} بـ cloudscraper: {e}")
            return None
    
    def fetch_with_selenium(self, url, timeout=30):
        """جلب المحتوى باستخدام Selenium"""
        driver = self.get_selenium_driver()
        if not driver:
            return None
        
        try:
            driver.get(url)
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(2)  # انتظار تحميل JavaScript
            return driver.page_source
        except Exception as e:
            logger.error(f"فشل جلب {url} بـ Selenium: {e}")
            return None
    
    def fetch_page_content(self, url):
        """جلب محتوى الصفحة بطرق متعددة"""
        methods = [
            self.fetch_with_requests,
            self.fetch_with_cloudscraper,
            self.fetch_with_selenium
        ]
        
        for method in methods:
            content = method(url)
            if content:
                return content
            time.sleep(random.uniform(1, 3))
        
        return None
    
    def extract_news_from_website(self, url, source_name):
        """استخراج الأخبار من موقع ويب"""
        content = self.fetch_page_content(url)
        if not content:
            return []
        
        soup = BeautifulSoup(content, 'html.parser')
        news_items = []
        
        # أنماط مختلفة لاستخراج الأخبار
        selectors = [
            'article',
            '.news-item',
            '.post',
            '.entry',
            '.story',
            'h1 a, h2 a, h3 a',
            '.title a',
            '.headline a'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                try:
                    news_item = self.extract_news_item(element, url, source_name)
                    if news_item and self.is_iraq_related(news_item['title']):
                        news_items.append(news_item)
                except Exception as e:
                    continue
        
        return news_items[:20]  # أقصى 20 خبر
    
    def extract_news_item(self, element, base_url, source_name):
        """استخراج عنصر خبر واحد"""
        title = ""
        link = ""
        content = ""
        
        # استخراج العنوان
        if element.name in ['h1', 'h2', 'h3']:
            title = element.get_text(strip=True)
            link_elem = element.find('a')
            if link_elem:
                link = link_elem.get('href', '')
        elif element.find(['h1', 'h2', 'h3']):
            title_elem = element.find(['h1', 'h2', 'h3'])
            title = title_elem.get_text(strip=True)
            link_elem = title_elem.find('a') or element.find('a')
            if link_elem:
                link = link_elem.get('href', '')
        else:
            title = element.get_text(strip=True)[:200]
            link_elem = element.find('a')
            if link_elem:
                link = link_elem.get('href', '')
        
        # تنظيف الرابط
        if link:
            link = urljoin(base_url, link)
        
        # استخراج المحتوى
        content_elem = element.find(['p', '.content', '.summary', '.excerpt'])
        if content_elem:
            content = content_elem.get_text(strip=True)
        
        if title and link and len(title) > 10:
            return {
                'title': title,
                'url': link,
                'content': content,
                'source': source_name
            }
        
        return None
    
    def extract_facebook_posts(self, facebook_url, source_name):
        """استخراج منشورات فيسبوك"""
        # تحويل رابط فيسبوك إلى النسخة المبسطة
        if 'facebook.com' in facebook_url:
            facebook_url = facebook_url.replace('www.facebook.com', 'm.facebook.com')
            facebook_url = facebook_url.replace('facebook.com', 'm.facebook.com')
        
        content = self.fetch_page_content(facebook_url)
        if not content:
            return []
        
        soup = BeautifulSoup(content, 'html.parser')
        posts = []
        
        # البحث عن المنشورات في النسخة المبسطة
        post_selectors = [
            'div[data-ft]',
            '.story_body_container',
            'article',
            '.userContentWrapper'
        ]
        
        for selector in post_selectors:
            elements = soup.select(selector)
            for element in elements:
                try:
                    post = self.extract_facebook_post(element, facebook_url, source_name)
                    if post and self.is_iraq_related(post['title']):
                        posts.append(post)
                except Exception as e:
                    continue
        
        return posts[:15]  # أقصى 15 منشور
    
    def extract_facebook_post(self, element, base_url, source_name):
        """استخراج منشور فيسبوك واحد"""
        text_content = element.get_text(strip=True)
        
        if len(text_content) < 20:
            return None
        
        # استخراج الرابط
        link_elem = element.find('a')
        post_url = base_url
        if link_elem and link_elem.get('href'):
            post_url = urljoin(base_url, link_elem.get('href'))
        
        # تنظيف النص
        title = text_content[:150] + "..." if len(text_content) > 150 else text_content
        
        return {
            'title': title,
            'url': post_url,
            'content': text_content,
            'source': source_name
        }
    
    def is_iraq_related(self, text):
        """فحص ما إذا كان النص متعلق بالعراق"""
        if not text:
            return False
        
        text_lower = text.lower()
        
        # فحص الكلمات المفتاحية العربية
        for keyword in IRAQ_KEYWORDS:
            if keyword in text_lower:
                return True
        
        # فحص الكلمات الإنجليزية
        english_keywords = ['iraq', 'iraqi', 'baghdad', 'basra', 'mosul', 'erbil']
        for keyword in english_keywords:
            if keyword in text_lower:
                return True
        
        return False
    
    def categorize_news(self, title, content):
        """تصنيف الأخبار"""
        text = (title + " " + content).lower()
        
        categories = {
            'سياسة': ['سياسة', 'حكومة', 'برلمان', 'وزير', 'رئيس', 'انتخابات'],
            'اقتصاد': ['اقتصاد', 'مالية', 'استثمار', 'بنك', 'نفط', 'تجارة'],
            'أمن': ['أمن', 'شرطة', 'جيش', 'إرهاب', 'عملية', 'اعتقال'],
            'رياضة': ['رياضة', 'كرة', 'فوز', 'مباراة', 'بطولة', 'لاعب'],
            'ثقافة': ['ثقافة', 'فن', 'مسرح', 'كتاب', 'شعر', 'أدب'],
            'صحة': ['صحة', 'طب', 'مستشفى', 'علاج', 'دواء', 'مرض']
        }
        
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in text:
                    return category
        
        return 'عام'
    
    def close(self):
        """إغلاق الموارد"""
        if self.driver:
            self.driver.quit()
        self.session.close()

def fetch_source_news(source_id, source_name, source_url, source_type):
    """جلب أخبار مصدر واحد"""
    scraper = NewsScraperEngine()
    news_items = []
    
    try:
        logger.info(f"بدء جلب أخبار من: {source_name}")
        
        if source_type == 'website':
            news_items = scraper.extract_news_from_website(source_url, source_name)
        elif source_type == 'facebook':
            news_items = scraper.extract_facebook_posts(source_url, source_name)
        elif source_type == 'rss':
            news_items = fetch_rss_news(source_url, source_name)
        
        # حفظ الأخبار في قاعدة البيانات
        saved_count = save_news_to_database(news_items, source_id)
        
        # تحديث إحصائيات المصدر
        update_source_stats(source_id, len(news_items), saved_count)
        
        logger.info(f"تم جلب {len(news_items)} خبر من {source_name}, حُفظ {saved_count}")
        
    except Exception as e:
        logger.error(f"خطأ في جلب أخبار {source_name}: {e}")
    finally:
        scraper.close()
    
    return len(news_items)

def fetch_rss_news(rss_url, source_name):
    """جلب أخبار RSS"""
    try:
        feed = feedparser.parse(rss_url)
        news_items = []
        
        for entry in feed.entries[:20]:
            title = entry.get('title', '')
            link = entry.get('link', '')
            content = entry.get('summary', '') or entry.get('description', '')
            
            if title and link:
                news_items.append({
                    'title': title,
                    'url': link,
                    'content': content,
                    'source': source_name
                })
        
        return news_items
    except Exception as e:
        logger.error(f"خطأ في جلب RSS من {rss_url}: {e}")
        return []

def save_news_to_database(news_items, source_id):
    """حفظ الأخبار في قاعدة البيانات"""
    if not news_items:
        return 0
    
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    saved_count = 0
    
    scraper = NewsScraperEngine()
    
    for item in news_items:
        try:
            # فحص عدم التكرار
            cursor.execute('SELECT id FROM news WHERE url = ?', (item['url'],))
            if cursor.fetchone():
                continue
            
            # تصنيف الخبر
            category = scraper.categorize_news(item['title'], item['content'])
            
            # حفظ الخبر
            cursor.execute('''
                INSERT INTO news (title, url, content, source_id, category, published_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                item['title'],
                item['url'],
                item['content'],
                source_id,
                category,
                datetime.now().strftime('%Y-%m-%d')
            ))
            
            saved_count += 1
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الخبر: {e}")
            continue
    
    conn.commit()
    conn.close()
    scraper.close()
    
    return saved_count

def update_source_stats(source_id, fetch_count, success_count):
    """تحديث إحصائيات المصدر"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        UPDATE sources 
        SET last_fetch = ?, 
            fetch_count = fetch_count + ?, 
            success_count = success_count + ?
        WHERE id = ?
    ''', (datetime.now(), fetch_count, success_count, source_id))
    
    conn.commit()
    conn.close()

def fetch_all_sources():
    """جلب أخبار جميع المصادر النشطة"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT id, name, url, type FROM sources WHERE active = 1')
    sources = cursor.fetchall()
    conn.close()
    
    total_news = 0
    
    for source in sources:
        source_id, name, url, source_type = source
        try:
            count = fetch_source_news(source_id, name, url, source_type)
            total_news += count
            
            # تأخير بين المصادر
            time.sleep(random.uniform(2, 5))
            
        except Exception as e:
            logger.error(f"خطأ في معالجة المصدر {name}: {e}")
            continue
    
    logger.info(f"انتهى جلب الأخبار. إجمالي الأخبار: {total_news}")
    return total_news

if __name__ == '__main__':
    # تشغيل جلب الأخبار
    fetch_all_sources()
