{% extends "base.html" %}

{% block title %}جميع الأخبار العراقية{% endblock %}

{% block extra_css %}
<style>
    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .filter-card {
        border: 2px solid var(--border-color);
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .filter-card:hover {
        border-color: var(--secondary-color);
    }

    .filter-card.active {
        border-color: var(--primary-color);
        background: rgba(52, 152, 219, 0.05);
    }

    .news-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }

    .news-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid var(--secondary-color);
    }

    .news-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .news-card-header {
        padding: 20px 20px 0;
    }

    .news-card-body {
        padding: 0 20px 20px;
    }

    .news-source {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }

    .source-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        object-fit: cover;
    }

    .source-name {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    .news-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-text);
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .news-title a {
        color: inherit;
        text-decoration: none;
    }

    .news-title a:hover {
        color: var(--secondary-color);
    }

    .news-summary {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 15px;
    }

    .news-meta {
        display: flex;
        justify-content: between;
        align-items: center;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .category-badge {
        background: var(--secondary-color);
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .stats-bar {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }

    .stats-item {
        display: inline-block;
        margin: 0 20px;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        display: block;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    @media (max-width: 768px) {
        .news-grid {
            grid-template-columns: 1fr;
        }
        
        .filters-section {
            padding: 15px;
        }
        
        .stats-item {
            display: block;
            margin: 10px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Stats Bar -->
<div class="stats-bar">
    <div class="stats-item">
        <span class="stats-number">{{ total_news }}</span>
        <span class="stats-label">إجمالي الأخبار</span>
    </div>
    <div class="stats-item">
        <span class="stats-number">{{ news|length }}</span>
        <span class="stats-label">أخبار الصفحة</span>
    </div>
    <div class="stats-item">
        <span class="stats-number">{{ sources|length }}</span>
        <span class="stats-label">مصادر نشطة</span>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section">
    <h5 class="mb-4">
        <i class="fas fa-filter me-2 text-primary"></i>
        تصفية الأخبار
    </h5>
    
    <form method="GET" action="{{ url_for('all_news') }}" id="filtersForm">
        <div class="row">
            <!-- Category Filter -->
            <div class="col-md-3 mb-3">
                <label class="form-label">الفئة</label>
                <select class="form-select" name="category" onchange="submitFilters()">
                    <option value="">جميع الفئات</option>
                    {% for category in categories %}
                        <option value="{{ category[0] }}" 
                                {% if current_category == category[0] %}selected{% endif %}>
                            {{ category[0] }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Source Filter -->
            <div class="col-md-3 mb-3">
                <label class="form-label">المصدر</label>
                <select class="form-select" name="source" onchange="submitFilters()">
                    <option value="">جميع المصادر</option>
                    {% for source in sources %}
                        <option value="{{ source[0] }}" 
                                {% if current_source == source[0]|string %}selected{% endif %}>
                            {{ source[1] }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Date Filter -->
            <div class="col-md-3 mb-3">
                <label class="form-label">التاريخ</label>
                <input type="date" class="form-control" name="date" 
                       value="{{ current_date }}" onchange="submitFilters()">
            </div>
            
            <!-- Actions -->
            <div class="col-md-3 mb-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('all_news') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- News Grid -->
{% if news %}
    <div class="news-grid">
        {% for item in news %}
        <article class="news-card">
            <div class="news-card-header">
                <div class="news-source">
                    {% if item[12] %}
                        <img src="{{ item[12] }}" alt="{{ item[11] }}" class="source-icon"
                             onerror="this.style.display='none'">
                    {% else %}
                        <div class="source-icon d-flex align-items-center justify-content-center bg-primary text-white fw-bold">
                            {{ item[11][0] }}
                        </div>
                    {% endif %}
                    <span class="source-name">{{ item[11] }}</span>
                </div>
                
                <h3 class="news-title">
                    <a href="{{ item[2] }}" target="_blank">{{ item[1] }}</a>
                </h3>
            </div>
            
            <div class="news-card-body">
                {% if item[3] %}
                <p class="news-summary">
                    {{ item[3][:150] }}{% if item[3]|length > 150 %}...{% endif %}
                </p>
                {% endif %}
                
                <div class="news-meta">
                    <div>
                        <i class="fas fa-calendar me-1"></i>
                        {{ item[7] if item[7] else 'غير محدد' }}
                    </div>
                    <div>
                        {% if item[5] %}
                            <span class="category-badge">{{ item[5] }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </article>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if total_news > per_page %}
    <nav aria-label="تصفح الأخبار" class="mt-4">
        <ul class="pagination justify-content-center">
            <!-- Previous Page -->
            {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('all_news', page=page-1, category=current_category, source=current_source, date=current_date) }}">
                        <i class="fas fa-chevron-right me-1"></i>
                        السابق
                    </a>
                </li>
            {% endif %}

            <!-- Page Numbers -->
            {% set total_pages = (total_news + per_page - 1) // per_page %}
            {% set start_page = [1, page - 2]|max %}
            {% set end_page = [start_page + 4, total_pages]|min %}

            {% if start_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('all_news', page=1, category=current_category, source=current_source, date=current_date) }}">1</a>
                </li>
                {% if start_page > 2 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
            {% endif %}

            {% for p in range(start_page, end_page + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('all_news', page=p, category=current_category, source=current_source, date=current_date) }}">{{ p }}</a>
                </li>
            {% endfor %}

            {% if end_page < total_pages %}
                {% if end_page < total_pages - 1 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('all_news', page=total_pages, category=current_category, source=current_source, date=current_date) }}">{{ total_pages }}</a>
                </li>
            {% endif %}

            <!-- Next Page -->
            {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('all_news', page=page+1, category=current_category, source=current_source, date=current_date) }}">
                        التالي
                        <i class="fas fa-chevron-left ms-1"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

{% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد أخبار</h4>
        <p class="text-muted">لم يتم العثور على أخبار تطابق المعايير المحددة</p>
        <div class="d-flex justify-content-center gap-2">
            <a href="{{ url_for('all_news') }}" class="btn btn-outline-primary">
                <i class="fas fa-undo me-1"></i>
                إعادة تعيين الفلاتر
            </a>
            <button class="btn btn-primary" onclick="fetchAllNews()">
                <i class="fas fa-sync-alt me-1"></i>
                جلب أخبار جديدة
            </button>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function submitFilters() {
    document.getElementById('filtersForm').submit();
}

function fetchAllNews() {
    if (!confirm('هل تريد جلب أخبار جديدة من جميع المصادر؟\nقد يستغرق هذا عدة دقائق.')) {
        return;
    }
    
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الجلب...';
    
    fetch('/api/fetch-news', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        button.disabled = false;
        button.innerHTML = originalText;
        
        if (data.success) {
            alert('تم بدء عملية جلب الأخبار!\nسيتم تحديث الصفحة بعد قليل.');
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        button.disabled = false;
        button.innerHTML = originalText;
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تدرجي لظهور البطاقات
    const newsCards = document.querySelectorAll('.news-card');
    newsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 50);
    });
    
    // تحديث عدد المشاهدات عند النقر على الروابط
    document.querySelectorAll('.news-title a').forEach(link => {
        link.addEventListener('click', function() {
            // يمكن إضافة API call لتحديث عدد المشاهدات
            console.log('تم النقر على:', this.textContent);
        });
    });
});
</script>
{% endblock %}
