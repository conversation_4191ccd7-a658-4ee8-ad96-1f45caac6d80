{% extends "base.html" %}

{% block title %}الأخبار - أداة الأخبار العراقية الذكية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-text me-2"></i>
                        الأخبار العراقية النصية - <span id="currentDateNews">اليوم</span>
                    </h4>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="refreshNews()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-success btn-sm" onclick="fetchNewNews()">
                            <i class="fas fa-download me-1"></i>جلب أخبار جديدة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="cleanAllNews()">
                            <i class="fas fa-trash me-1"></i>تنظيف الأخبار
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="newsContainer">
                    {% if news %}
                        <table class="table table-bordered table-hover text-center align-middle">
                            <thead class="table-dark">
                                <tr>
                                    <th>العنوان</th>
                                    <th>تاريخ الخبر</th>
                                    <th>المصدر</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in news %}
                                <tr>
                                    <td class="text-start">
                                        {% if item[3] %}
                                            <span class="news-title-link" style="cursor:pointer; color:inherit; text-decoration:none;" onclick="openNewsUrl('{{ item[3] }}')">{{ item[0] }}</span>
                                        {% else %}
                                            {{ item[0] }}
                                        {% endif %}
                                    </td>
                                    <td>{{ item[1] if item[1] else 'اليوم' }}</td>
                                    <td>{{ item[2] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أخبار متاحة</h5>
                            <p class="text-muted">لم يتم العثور على أخبار عراقية لليوم الحالي</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal التحميل -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري جلب الأخبار...</h5>
                <p class="text-muted">يرجى الانتظار بينما نجلب أحدث الأخبار</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function openNewsUrl(url) {
    if(url) window.open(url, '_blank');
}
function refreshNews() {
    location.reload();
}
function fetchNewNews() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
    fetch('/api/fetch-news', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(function(data) {
        modal.hide();
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        modal.hide();
        alert('حدث خطأ في الاتصال');
    });
}
function cleanAllNews() {
    if (!confirm('هل أنت متأكد أنك تريد حذف جميع الأخبار الحالية؟')) return;
    fetch('/api/clean-news', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال');
    });
}
</script>
{% endblock %}