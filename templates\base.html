<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}أداة الأخبار العراقية الذكية{% endblock %}</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c3e50 !important;
            font-size: 1.5rem;
        }
        
        .nav-link {
            font-weight: 500;
            color: #34495e !important;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #3498db !important;
            transform: translateY(-2px);
        }
        
        .main-content {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #ecf0f1;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .footer {
            background: rgba(44, 62, 80, 0.9);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: auto;
        }
        
        .news-item {
            border-right: 4px solid #3498db;
            padding-right: 15px;
            margin-bottom: 20px;
        }
        
        .news-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .news-meta {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #3498db;
        }

        /* تحسينات القوائم المنسدلة */
        .dropdown-menu {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            margin-top: 10px;
        }

        .dropdown-item {
            padding: 12px 20px;
            transition: all 0.3s ease;
            border-radius: 10px;
            margin: 5px;
        }

        .dropdown-item:hover {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            transform: translateX(-5px);
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* تأثير خاص للصفحات المحسنة */
        .dropdown-item:nth-child(2) {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            font-weight: 600;
        }

        .dropdown-item:nth-child(2):hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
            transform: translateX(-5px) scale(1.02);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-newspaper me-2"></i>
                أداة الأخبار العراقية الذكية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="newsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-newspaper me-1"></i>الأخبار
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('news') }}">
                                <i class="fas fa-list me-1"></i>الأخبار (عادي)
                            </a></li>
                            <li><a class="dropdown-item" href="/news-enhanced">
                                <i class="fas fa-star me-1"></i>الأخبار المحسنة
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="sourcesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-rss me-1"></i>المصادر
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('sources') }}">
                                <i class="fas fa-list me-1"></i>المصادر (عادي)
                            </a></li>
                            <li><a class="dropdown-item" href="/sources-enhanced">
                                <i class="fas fa-star me-1"></i>إدارة المصادر المحسنة
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-content" style="margin-top: 100px;">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <p class="mb-0">© 2024 أداة الأخبار العراقية الذكية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
