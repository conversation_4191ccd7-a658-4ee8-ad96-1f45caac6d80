#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح روابط فيسبوك
"""

def convert_facebook_url(url):
    """تحويل رابط فيسبوك إلى النسخة المبسطة بشكل صحيح"""
    if not url or 'facebook.com' not in url:
        return url
    
    # إذا كان الرابط يحتوي على m.facebook.com بالفعل، لا نغيره
    if 'm.facebook.com' in url:
        return url
    
    # تنظيف الرابط أولاً
    clean_url = url.strip()
    
    # إضافة https إذا لم يكن موجود
    if not clean_url.startswith(('http://', 'https://')):
        clean_url = 'https://' + clean_url
    
    # تحويل إلى النسخة المبسطة
    if 'www.facebook.com' in clean_url:
        clean_url = clean_url.replace('www.facebook.com', 'm.facebook.com')
    elif 'facebook.com' in clean_url and 'm.facebook.com' not in clean_url:
        clean_url = clean_url.replace('facebook.com', 'm.facebook.com')
    
    return clean_url

def test_facebook_urls():
    """اختبار تحويل روابط فيسبوك مختلفة"""
    
    test_urls = [
        'https://www.facebook.com/shafaqnews',
        'https://facebook.com/AlSumaria',
        'facebook.com/rudawnews',
        'www.facebook.com/AlArabiya.Iraq',
        'https://m.facebook.com/IraqiNews',  # يجب أن يبقى كما هو
        'https://www.facebook.com/pages/test/123456'
    ]
    
    print("🧪 اختبار تحويل روابط فيسبوك:")
    print("=" * 60)
    
    for url in test_urls:
        converted = convert_facebook_url(url)
        status = "✅" if 'm.facebook.com' in converted else "❌"
        
        print(f"{status} الأصلي: {url}")
        print(f"   المحول: {converted}")
        print()
    
    # اختبار الحالات الخاصة
    print("🔍 اختبار الحالات الخاصة:")
    print("-" * 40)
    
    special_cases = [
        '',  # رابط فارغ
        'https://google.com',  # ليس فيسبوك
        'https://m.facebook.com/test',  # محول مسبقاً
        'facebook.com/test',  # بدون https
    ]
    
    for url in special_cases:
        converted = convert_facebook_url(url)
        print(f"الأصلي: '{url}' -> المحول: '{converted}'")

if __name__ == "__main__":
    test_facebook_urls()
