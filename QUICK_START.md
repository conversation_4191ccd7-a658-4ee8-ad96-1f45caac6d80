# 🚀 دليل التشغيل السريع - موسوعة أخبار العراق

## ⚡ التشغيل السريع (3 خطوات)

### **الخطوة 1: تثبيت المتطلبات**
```bash
python install.py
```

### **الخطوة 2: تشغيل النظام**
```bash
python start.py
```

### **الخطوة 3: فتح المتصفح**
```
http://localhost:5008
```

---

## 🛠️ طرق التشغيل البديلة

### **الطريقة 1: التشغيل المباشر**
```bash
python app.py
```

### **الطريقة 2: باستخدام run.py**
```bash
python run.py
```

### **الطريقة 3: تثبيت يدوي**
```bash
pip install Flask requests beautifulsoup4 lxml
python app.py
```

---

## 🔧 حل المشاكل الشائعة

### **مشكلة: ModuleNotFoundError**
```bash
# الحل:
pip install Flask
# أو
python install.py
```

### **مشكلة: خطأ في قاعدة البيانات**
```bash
# احذف قاعدة البيانات وأعد إنشاءها:
del iraq_news.db
python start.py
```

### **مشكلة: المنفذ مستخدم**
```bash
# غيّر المنفذ في app.py:
app.run(host='0.0.0.0', port=5009, debug=True)
```

---

## 📱 الوصول للنظام

### **المحلي:**
- http://localhost:5008
- http://127.0.0.1:5008

### **الشبكة المحلية:**
- http://[your-ip]:5008
- مثال: http://*************:5008

---

## 🎯 الصفحات المتاحة

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| الرئيسية | `/` | عرض المصادر |
| جميع الأخبار | `/news` | الأخبار مع فلاتر |
| لوحة الإدارة | `/admin` | إدارة المصادر |
| إضافة مصدر | `/add_source` | إضافة مصدر جديد |

---

## 📊 الميزات الأساسية

### ✅ **متوفرة الآن:**
- عرض المصادر في بطاقات جذابة
- إضافة مصادر جديدة
- لوحة إدارة شاملة
- تصميم متجاوب وعصري
- قاعدة بيانات SQLite

### 🔄 **قيد التطوير:**
- جلب الأخبار التلقائي
- فلترة الأخبار العراقية
- دعم فيسبوك و RSS
- إحصائيات مفصلة

---

## 💡 نصائح الاستخدام

1. **ابدأ بإضافة مصادر** من لوحة الإدارة
2. **استخدم أسماء واضحة** للمصادر
3. **تأكد من صحة الروابط** قبل الإضافة
4. **راقب السجلات** في مجلد logs/

---

## 🆘 طلب المساعدة

إذا واجهت مشاكل:

1. **تأكد من تثبيت Python 3.7+**
2. **جرب تثبيت المتطلبات يدوياً**
3. **تحقق من ملفات السجلات**
4. **أعد تشغيل النظام**

---

## 🎉 استمتع بالاستخدام!

النظام الآن جاهز للاستخدام. ابدأ بإضافة مصادر الأخبار العراقية المفضلة لديك!
