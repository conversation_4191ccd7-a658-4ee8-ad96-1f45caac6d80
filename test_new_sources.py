#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار المصادر الجديدة
"""

import requests
import json

def test_new_sources():
    """اختبار المصادر الجديدة المضافة"""
    
    # مصادر للاختبار
    test_sources = [
        {
            "name": "شفق نيوز - أخبار العراق",
            "url": "https://shafaq.com/ar/iraq",
            "type": "website"
        },
        {
            "name": "السومرية نيوز - العراق", 
            "url": "https://www.alsumaria.tv/news/iraq",
            "type": "website"
        },
        {
            "name": "وكالة الأنباء العراقية",
            "url": "https://www.ina.iq/news",
            "type": "website"
        }
    ]
    
    api_url = "http://localhost:5008/api/test-source"
    
    print("🧪 اختبار المصادر الجديدة")
    print("=" * 50)
    
    successful_tests = 0
    total_tests = len(test_sources)
    
    for i, source in enumerate(test_sources, 1):
        print(f"\n📊 اختبار {i}/{total_tests}: {source['name']}")
        print(f"🔗 الرابط: {source['url']}")
        
        try:
            response = requests.post(
                api_url, 
                json=source, 
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success', False):
                    print(f"✅ نجح الاختبار!")
                    print(f"📈 عدد العناوين: {result.get('count', 0)}")
                    
                    if 'iraq_related' in result:
                        print(f"🇮🇶 العناوين العراقية: {result['iraq_related']}")
                    
                    successful_tests += 1
                    
                else:
                    print(f"⚠️  فشل الاختبار: {result.get('message', 'لا توجد رسالة')}")
                    
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                print(f"📝 الرسالة: {response.text[:100]}...")
                
        except requests.exceptions.ConnectionError:
            print("❌ خطأ: لا يمكن الاتصال بالخادم")
            print("💡 تأكد من تشغيل التطبيق على http://localhost:5008")
            break
            
        except requests.exceptions.Timeout:
            print("⏰ انتهت مهلة الانتظار (30 ثانية)")
            
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {str(e)}")
    
    # النتائج النهائية
    print(f"\n📊 ملخص النتائج:")
    print(f"✅ نجح: {successful_tests}/{total_tests} مصدر")
    print(f"❌ فشل: {total_tests - successful_tests}/{total_tests} مصدر")
    
    success_rate = (successful_tests / total_tests) * 100
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print(f"🎉 ممتاز! المصادر الجديدة تعمل بكفاءة عالية")
    elif success_rate >= 60:
        print(f"👍 جيد! معظم المصادر تعمل بشكل صحيح")
    else:
        print(f"⚠️  يحتاج تحسين! بعض المصادر تحتاج مراجعة")
    
    print(f"\n💡 التوصيات:")
    if successful_tests > 0:
        print(f"• المصادر الناجحة ستوفر أخبار عراقية موثوقة")
        print(f"• لا توجد قيود من فيسبوك أو حماية معقدة")
        print(f"• سرعة أعلى في جلب الأخبار")
    
    if successful_tests < total_tests:
        print(f"• راجع المصادر الفاشلة وتأكد من صحة الروابط")
        print(f"• بعض المواقع قد تكون بطيئة أو غير متاحة مؤقتاً")

if __name__ == "__main__":
    test_new_sources()
