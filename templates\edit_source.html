{% extends "base.html" %}

{% block title %}تعديل المصدر - أداة الأخبار العراقية الذكية{% endblock %}

{% block extra_css %}
<style>
    .edit-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }
    .btn-test {
        background: linear-gradient(45deg, #27ae60, #2ecc71);
        border: none;
        color: white;
    }
    .btn-test:hover {
        background: linear-gradient(45deg, #229954, #27ae60);
        color: white;
    }
    .test-result {
        margin-top: 15px;
        padding: 12px;
        border-radius: 8px;
        font-size: 0.9em;
    }
    .test-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .test-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .test-loading {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    .source-preview {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <!-- العنوان والتنقل -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-edit text-primary me-2"></i>
                تعديل المصدر
            </h2>
            <a href="/sources" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للمصادر
            </a>
        </div>

        <!-- نموذج التعديل -->
        <div class="edit-card p-4">
            <form method="POST" action="/update_source/{{ source[0] }}" id="editForm">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-1 text-primary"></i>
                            اسم المصدر
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ source[1] }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="type" class="form-label">
                            <i class="fas fa-layer-group me-1 text-primary"></i>
                            نوع المصدر
                        </label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="website" {% if source[3] == 'website' %}selected{% endif %}>
                                <i class="fas fa-globe"></i> موقع ويب
                            </option>
                            <option value="facebook" {% if source[3] == 'facebook' %}selected{% endif %}>
                                <i class="fab fa-facebook"></i> فيسبوك
                            </option>
                            <option value="telegram" {% if source[3] == 'telegram' %}selected{% endif %}>
                                <i class="fab fa-telegram"></i> تلغرام
                            </option>
                            <option value="rss" {% if source[3] == 'rss' %}selected{% endif %}>
                                <i class="fas fa-rss"></i> RSS Feed
                            </option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="url" class="form-label">
                        <i class="fas fa-link me-1 text-primary"></i>
                        رابط المصدر
                    </label>
                    <input type="url" class="form-control" id="url" name="url" 
                           value="{{ source[2] }}" required>
                    <div class="form-text">
                        <small>
                            <strong>أمثلة حسب النوع:</strong><br>
                            • موقع ويب: https://shafaq.com<br>
                            • فيسبوك: https://www.facebook.com/pagename<br>
                            • تلغرام: https://t.me/channelname<br>
                            • RSS: https://example.com/rss.xml
                        </small>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="active" name="active" 
                               {% if source[4] == 1 %}checked{% endif %}>
                        <label class="form-check-label" for="active">
                            <i class="fas fa-toggle-on me-1 text-success"></i>
                            المصدر نشط
                        </label>
                        <div class="form-text">
                            <small>المصادر النشطة فقط يتم جلب الأخبار منها تلقائياً</small>
                        </div>
                    </div>
                </div>

                <!-- معاينة المصدر -->
                <div class="source-preview">
                    <h6 class="mb-3">
                        <i class="fas fa-eye me-1"></i>
                        معاينة المصدر
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>الاسم:</strong> <span id="preview-name">{{ source[1] }}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>النوع:</strong> <span id="preview-type">{{ source[3] }}</span>
                        </div>
                        <div class="col-12 mt-2">
                            <strong>الرابط:</strong> 
                            <a href="{{ source[2] }}" target="_blank" id="preview-url" class="text-decoration-none">
                                {{ source[2] }}
                            </a>
                        </div>
                        <div class="col-12 mt-2">
                            <strong>الحالة:</strong> 
                            <span id="preview-status" class="badge {% if source[4] == 1 %}bg-success{% else %}bg-danger{% endif %}">
                                {% if source[4] == 1 %}نشط{% else %}معطل{% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <button type="button" class="btn btn-test" onclick="testCurrentSource()">
                            <i class="fas fa-vial me-1"></i>
                            اختبار المصدر
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewChanges()">
                            <i class="fas fa-eye me-1"></i>
                            معاينة التغييرات
                        </button>
                    </div>
                    <div>
                        <a href="/sources" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>

                <!-- نتيجة الاختبار -->
                <div id="test-result" class="test-result" style="display: none;"></div>
            </form>
        </div>

        <!-- معلومات إضافية -->
        <div class="edit-card p-4 mt-4">
            <h6 class="mb-3">
                <i class="fas fa-info-circle me-1 text-info"></i>
                معلومات المصدر
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>تاريخ الإضافة:</strong> {{ source[5] if source[5] else 'غير محدد' }}</p>
                    <p><strong>معرف المصدر:</strong> #{{ source[0] }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>آخر تحديث:</strong> الآن</p>
                    <p><strong>حالة الاختبار:</strong> <span id="last-test-status">لم يتم الاختبار</span></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testCurrentSource() {
    const url = document.getElementById('url').value;
    const type = document.getElementById('type').value;
    const resultDiv = document.getElementById('test-result');
    
    if (!url || !type) {
        alert('يرجى ملء جميع الحقول أولاً');
        return;
    }
    
    // إظهار حالة التحميل
    resultDiv.className = 'test-result test-loading';
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري اختبار المصدر...';
    
    fetch('/api/test-source', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            url: url,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                <strong>نجح الاختبار!</strong>
                <br>العناوين المكتشفة: ${data.count || 0}
                <br>الأخبار العراقية: ${data.iraq_related || 0}
                <br><small class="text-muted">${data.message || ''}</small>
            `;
            document.getElementById('last-test-status').innerHTML = '<span class="text-success">نجح</span>';
        } else {
            resultDiv.className = 'test-result test-error';
            resultDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>فشل الاختبار</strong>
                <br><small>${data.message || 'خطأ غير معروف'}</small>
            `;
            document.getElementById('last-test-status').innerHTML = '<span class="text-danger">فشل</span>';
        }
    })
    .catch(error => {
        resultDiv.className = 'test-result test-error';
        resultDiv.innerHTML = `
            <i class="fas fa-times-circle me-2"></i>
            <strong>خطأ في الاتصال</strong>
            <br><small>${error.message}</small>
        `;
        document.getElementById('last-test-status').innerHTML = '<span class="text-danger">خطأ</span>';
    });
}

function previewChanges() {
    const name = document.getElementById('name').value;
    const type = document.getElementById('type').value;
    const url = document.getElementById('url').value;
    const active = document.getElementById('active').checked;
    
    // تحديث المعاينة
    document.getElementById('preview-name').textContent = name || 'غير محدد';
    document.getElementById('preview-type').textContent = type || 'غير محدد';
    document.getElementById('preview-url').textContent = url || 'غير محدد';
    document.getElementById('preview-url').href = url || '#';
    
    const statusSpan = document.getElementById('preview-status');
    if (active) {
        statusSpan.className = 'badge bg-success';
        statusSpan.textContent = 'نشط';
    } else {
        statusSpan.className = 'badge bg-danger';
        statusSpan.textContent = 'معطل';
    }
    
    // تمرير سلس للمعاينة
    document.querySelector('.source-preview').scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
    });
}

// تحديث المعاينة تلقائياً عند تغيير القيم
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['name', 'type', 'url', 'active'];
    
    inputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', previewChanges);
            element.addEventListener('change', previewChanges);
        }
    });
    
    // تحسين placeholder حسب النوع
    const typeSelect = document.getElementById('type');
    const urlInput = document.getElementById('url');
    
    typeSelect.addEventListener('change', function() {
        const type = this.value;
        let placeholder = 'https://example.com';
        
        switch(type) {
            case 'website':
                placeholder = 'https://shafaq.com';
                break;
            case 'facebook':
                placeholder = 'https://www.facebook.com/pagename';
                break;
            case 'telegram':
                placeholder = 'https://t.me/channelname';
                break;
            case 'rss':
                placeholder = 'https://example.com/rss.xml';
                break;
        }
        
        urlInput.placeholder = placeholder;
    });
    
    // تأكيد قبل المغادرة إذا كانت هناك تغييرات
    let formChanged = false;
    const form = document.getElementById('editForm');
    const originalData = new FormData(form);
    
    form.addEventListener('input', function() {
        formChanged = true;
    });
    
    window.addEventListener('beforeunload', function(e) {
        if (formChanged) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
    
    // إزالة التحذير عند الإرسال
    form.addEventListener('submit', function() {
        formChanged = false;
    });
});
</script>
{% endblock %}
