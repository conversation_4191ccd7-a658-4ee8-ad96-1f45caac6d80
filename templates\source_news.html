{% extends "base.html" %}

{% block title %}{{ source[1] }} - أخبار المصدر{% endblock %}

{% block extra_css %}
<style>
    .source-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 30px 0;
        border-radius: 20px;
        margin-bottom: 30px;
    }

    .source-info {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .source-logo {
        width: 80px;
        height: 80px;
        border-radius: 15px;
        border: 3px solid rgba(255,255,255,0.3);
        object-fit: cover;
    }

    .news-item {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid var(--secondary-color);
    }

    .news-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .news-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 10px;
        line-height: 1.4;
    }

    .news-title a {
        color: inherit;
        text-decoration: none;
    }

    .news-title a:hover {
        color: var(--secondary-color);
    }

    .news-meta {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .news-content {
        color: #495057;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .category-tag {
        background: var(--secondary-color);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .pagination-wrapper {
        background: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 30px;
    }

    .page-link {
        border-radius: 10px;
        margin: 0 2px;
        border: none;
        color: var(--primary-color);
    }

    .page-link:hover {
        background: var(--secondary-color);
        color: white;
    }

    .page-item.active .page-link {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .source-info {
            flex-direction: column;
            text-align: center;
        }
        
        .source-logo {
            width: 60px;
            height: 60px;
        }
        
        .news-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Source Header -->
<div class="source-header">
    <div class="container">
        <div class="source-info">
            {% if source[6] %}
                <img src="{{ source[6] }}" alt="{{ source[1] }}" class="source-logo"
                     onerror="this.style.display='none'">
            {% else %}
                <div class="source-logo d-flex align-items-center justify-content-center bg-white text-primary fw-bold fs-2">
                    {{ source[1][0] }}
                </div>
            {% endif %}
            
            <div class="flex-grow-1">
                <h1 class="mb-2">{{ source[1] }}</h1>
                <p class="mb-2 opacity-75">{{ source[4] }}</p>
                <div class="d-flex gap-3 align-items-center">
                    <span class="badge bg-light text-dark">{{ source[3] }}</span>
                    <span><i class="fas fa-newspaper me-1"></i>{{ total_news }} خبر</span>
                    {% if source[8] %}
                        <span><i class="fas fa-clock me-1"></i>آخر جلب: {{ source[8] }}</span>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <a href="{{ source[2] }}" target="_blank" class="btn btn-light btn-lg">
                    <i class="fas fa-external-link-alt me-2"></i>
                    زيارة المصدر
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Navigation -->
<div class="row mb-4">
    <div class="col-md-6">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('index') }}">الرئيسية</a>
                </li>
                <li class="breadcrumb-item active">{{ source[1] }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" onclick="fetchSourceNews()">
            <i class="fas fa-sync-alt me-2"></i>
            جلب أخبار جديدة
        </button>
    </div>
</div>

<!-- News List -->
{% if news %}
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-list me-2 text-primary"></i>
                أخبار {{ source[1] }}
                <small class="text-muted">(الصفحة {{ page }} من {{ (total_news + per_page - 1) // per_page }})</small>
            </h3>
        </div>
    </div>

    {% for item in news %}
    <article class="news-item">
        <div class="news-meta">
            <div>
                <i class="fas fa-calendar me-1"></i>
                {{ item[7] if item[7] else 'غير محدد' }}
            </div>
            <div>
                {% if item[5] %}
                    <span class="category-tag">{{ item[5] }}</span>
                {% endif %}
            </div>
        </div>
        
        <h2 class="news-title">
            <a href="{{ item[2] }}" target="_blank">{{ item[1] }}</a>
        </h2>
        
        {% if item[3] %}
        <div class="news-content">
            {{ item[3][:300] }}{% if item[3]|length > 300 %}...{% endif %}
        </div>
        {% endif %}
        
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                <i class="fas fa-eye me-1"></i>
                {{ item[9] if item[9] else 0 }} مشاهدة
            </small>
            <a href="{{ item[2] }}" target="_blank" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-external-link-alt me-1"></i>
                قراءة المزيد
            </a>
        </div>
    </article>
    {% endfor %}

    <!-- Pagination -->
    {% if total_news > per_page %}
    <div class="pagination-wrapper">
        <nav aria-label="تصفح الأخبار">
            <ul class="pagination justify-content-center mb-0">
                <!-- Previous Page -->
                {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('source_news', source_id=source[0], page=page-1) }}">
                            <i class="fas fa-chevron-right me-1"></i>
                            السابق
                        </a>
                    </li>
                {% endif %}

                <!-- Page Numbers -->
                {% set start_page = [1, page - 2]|max %}
                {% set end_page = [start_page + 4, (total_news + per_page - 1) // per_page]|min %}

                {% if start_page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('source_news', source_id=source[0], page=1) }}">1</a>
                    </li>
                    {% if start_page > 2 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endif %}

                {% for p in range(start_page, end_page + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('source_news', source_id=source[0], page=p) }}">{{ p }}</a>
                    </li>
                {% endfor %}

                {% if end_page < (total_news + per_page - 1) // per_page %}
                    {% if end_page < (total_news + per_page - 1) // per_page - 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('source_news', source_id=source[0], page=(total_news + per_page - 1) // per_page) }}">
                            {{ (total_news + per_page - 1) // per_page }}
                        </a>
                    </li>
                {% endif %}

                <!-- Next Page -->
                {% if page < (total_news + per_page - 1) // per_page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('source_news', source_id=source[0], page=page+1) }}">
                            التالي
                            <i class="fas fa-chevron-left ms-1"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}

{% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-newspaper"></i>
        <h4>لا توجد أخبار</h4>
        <p>لم يتم جلب أي أخبار من هذا المصدر بعد</p>
        <button class="btn btn-primary" onclick="fetchSourceNews()">
            <i class="fas fa-sync-alt me-2"></i>
            جلب أخبار الآن
        </button>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function fetchSourceNews() {
    if (!confirm('هل تريد جلب أخبار جديدة من {{ source[1] }}؟\nقد يستغرق هذا بعض الوقت.')) {
        return;
    }
    
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الجلب...';
    
    // محاكاة عملية الجلب
    setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalText;
        
        alert('تم جلب أخبار جديدة!\nسيتم تحديث الصفحة.');
        location.reload();
    }, 5000);
}

// تحديث عدد المشاهدات عند النقر على الروابط
document.querySelectorAll('.news-title a').forEach(link => {
    link.addEventListener('click', function() {
        // يمكن إضافة API call لتحديث عدد المشاهدات
        console.log('تم النقر على:', this.textContent);
    });
});

// تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تدرجي لظهور الأخبار
    const newsItems = document.querySelectorAll('.news-item');
    newsItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
