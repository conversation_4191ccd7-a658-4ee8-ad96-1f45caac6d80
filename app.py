#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
موسوعة أخبار العراق - نظام جلب وعرض الأخبار العراقية
مستوحى من تصميم 964media.com
"""

import os
import sqlite3
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import threading
import time

# إعداد المجلدات
os.makedirs('templates', exist_ok=True)
os.makedirs('static/css', exist_ok=True)
os.makedirs('static/js', exist_ok=True)
os.makedirs('static/images', exist_ok=True)
os.makedirs('logs', exist_ok=True)

# إعداد التطبيق
app = Flask(__name__)
app.secret_key = 'iraq_news_encyclopedia_2025'
app.config['JSON_AS_ASCII'] = False

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# متغيرات عامة
stop_fetching = False

def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    # جدول المصادر
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sources (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            url TEXT NOT NULL UNIQUE,
            type TEXT NOT NULL,
            category TEXT DEFAULT 'عام',
            active INTEGER DEFAULT 1,
            icon_url TEXT,
            description TEXT,
            last_fetch TIMESTAMP,
            fetch_count INTEGER DEFAULT 0,
            success_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الأخبار
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS news (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            url TEXT NOT NULL UNIQUE,
            content TEXT,
            summary TEXT,
            source_id INTEGER,
            category TEXT DEFAULT 'عام',
            published_date DATE,
            fetch_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_iraq_related INTEGER DEFAULT 1,
            priority INTEGER DEFAULT 1,
            views INTEGER DEFAULT 0,
            FOREIGN KEY (source_id) REFERENCES sources (id)
        )
    ''')
    
    # جدول الإحصائيات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATE UNIQUE,
            total_news INTEGER DEFAULT 0,
            iraq_news INTEGER DEFAULT 0,
            sources_active INTEGER DEFAULT 0,
            fetch_attempts INTEGER DEFAULT 0,
            fetch_success INTEGER DEFAULT 0
        )
    ''')
    
    # إدراج مصادر افتراضية
    default_sources = [
        ('شفق نيوز', 'https://shafaq.com', 'website', 'أخبار عامة', 'https://www.shafaq.com/assets/imgs/shafaq.png'),
        ('السومرية', 'https://www.alsumaria.tv', 'website', 'أخبار عامة', 'https://www.alsumaria.tv/Favicon.ico'),
        ('الفرات نيوز', 'https://alforatnews.iq', 'website', 'أخبار عامة', 'https://alforatnews.iq/asset/theme-alforat/image/touch-icon-iphone-retina.png'),
        ('واع', 'https://www.ina.iq', 'website', 'وكالة رسمية', 'https://www.ina.iq/templates/Default/images/icon128.png'),
        ('المعلومة', 'https://almaalomah.me', 'website', 'أخبار عامة', None),
        ('بغداد اليوم', 'https://baghdadtoday.news', 'website', 'أخبار عامة', None),
        ('الصباح', 'https://alsabaah.iq', 'website', 'صحيفة رسمية', 'https://alsabaah.iq/templates/Default/img/icon.png'),
        ('دجلة', 'https://dijlah.tv', 'website', 'قناة تلفزيونية', 'https://dijlah.tv/wp-content/uploads/2025/03/cropped-Fav-32x32.png'),
    ]
    
    for source in default_sources:
        cursor.execute('''
            INSERT OR IGNORE INTO sources (name, url, type, category, icon_url)
            VALUES (?, ?, ?, ?, ?)
        ''', source)
    
    conn.commit()
    conn.close()
    logger.info("تم إنشاء قاعدة البيانات بنجاح")

@app.route('/')
def index():
    """الصفحة الرئيسية - عرض المصادر مثل 964media"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    # جلب المصادر النشطة
    cursor.execute('''
        SELECT s.*, COUNT(n.id) as news_count
        FROM sources s
        LEFT JOIN news n ON s.id = n.source_id 
        WHERE s.active = 1
        GROUP BY s.id
        ORDER BY s.category, s.name
    ''')
    sources = cursor.fetchall()
    
    # جلب الإحصائيات
    cursor.execute('SELECT COUNT(*) FROM sources WHERE active = 1')
    active_sources = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM news WHERE date(fetch_date) = date("now")')
    today_news = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM news')
    total_news = cursor.fetchone()[0]
    
    conn.close()
    
    return render_template('index.html', 
                         sources=sources,
                         active_sources=active_sources,
                         today_news=today_news,
                         total_news=total_news)

@app.route('/source/<int:source_id>')
def source_news(source_id):
    """عرض أخبار مصدر معين"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    # جلب معلومات المصدر
    cursor.execute('SELECT * FROM sources WHERE id = ?', (source_id,))
    source = cursor.fetchone()
    
    if not source:
        flash('المصدر غير موجود', 'error')
        return redirect(url_for('index'))
    
    # جلب أخبار المصدر
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    cursor.execute('''
        SELECT * FROM news 
        WHERE source_id = ? 
        ORDER BY fetch_date DESC 
        LIMIT ? OFFSET ?
    ''', (source_id, per_page, offset))
    news = cursor.fetchall()
    
    # عدد الأخبار الإجمالي
    cursor.execute('SELECT COUNT(*) FROM news WHERE source_id = ?', (source_id,))
    total_news = cursor.fetchone()[0]
    
    conn.close()
    
    return render_template('source_news.html', 
                         source=source, 
                         news=news,
                         page=page,
                         total_news=total_news,
                         per_page=per_page)

@app.route('/news')
def all_news():
    """عرض جميع الأخبار"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    # فلاتر
    category = request.args.get('category', '')
    date_filter = request.args.get('date', '')
    source_filter = request.args.get('source', '')
    
    # بناء الاستعلام
    where_conditions = ['1=1']
    params = []
    
    if category:
        where_conditions.append('n.category = ?')
        params.append(category)
    
    if date_filter:
        where_conditions.append('date(n.fetch_date) = ?')
        params.append(date_filter)
    
    if source_filter:
        where_conditions.append('n.source_id = ?')
        params.append(source_filter)
    
    where_clause = ' AND '.join(where_conditions)
    
    # جلب الأخبار
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    cursor.execute(f'''
        SELECT n.*, s.name as source_name, s.icon_url
        FROM news n
        JOIN sources s ON n.source_id = s.id
        WHERE {where_clause}
        ORDER BY n.fetch_date DESC
        LIMIT ? OFFSET ?
    ''', params + [per_page, offset])
    news = cursor.fetchall()
    
    # عدد الأخبار الإجمالي
    cursor.execute(f'''
        SELECT COUNT(*)
        FROM news n
        JOIN sources s ON n.source_id = s.id
        WHERE {where_clause}
    ''', params)
    total_news = cursor.fetchone()[0]
    
    # جلب الفئات والمصادر للفلاتر
    cursor.execute('SELECT DISTINCT category FROM news ORDER BY category')
    categories = cursor.fetchall()
    
    cursor.execute('SELECT id, name FROM sources WHERE active = 1 ORDER BY name')
    sources = cursor.fetchall()
    
    conn.close()
    
    return render_template('news.html',
                         news=news,
                         categories=categories,
                         sources=sources,
                         page=page,
                         total_news=total_news,
                         per_page=per_page,
                         current_category=category,
                         current_date=date_filter,
                         current_source=source_filter)

@app.route('/admin')
def admin():
    """لوحة الإدارة"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()
    
    # إحصائيات عامة
    cursor.execute('SELECT COUNT(*) FROM sources')
    total_sources = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM sources WHERE active = 1')
    active_sources = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM news')
    total_news = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM news WHERE date(fetch_date) = date("now")')
    today_news = cursor.fetchone()[0]
    
    # آخر عمليات الجلب
    cursor.execute('''
        SELECT s.name, s.last_fetch, s.fetch_count, s.success_count
        FROM sources s
        WHERE s.last_fetch IS NOT NULL
        ORDER BY s.last_fetch DESC
        LIMIT 10
    ''')
    recent_fetches = cursor.fetchall()
    
    conn.close()
    
    return render_template('admin.html',
                         total_sources=total_sources,
                         active_sources=active_sources,
                         total_news=total_news,
                         today_news=today_news,
                         recent_fetches=recent_fetches)

@app.route('/api/fetch-news', methods=['POST'])
def api_fetch_news():
    """API لجلب الأخبار"""
    try:
        # محاكاة جلب الأخبار (سيتم تطويرها لاحقاً)
        logger.info("تم طلب جلب الأخبار")

        return jsonify({
            'success': True,
            'message': 'تم بدء عملية جلب الأخبار (محاكاة)'
        })
    except Exception as e:
        logger.error(f"خطأ في API جلب الأخبار: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@app.route('/api/stats')
def api_stats():
    """API للإحصائيات"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()

    # إحصائيات اليوم
    today = datetime.now().strftime('%Y-%m-%d')

    cursor.execute('SELECT COUNT(*) FROM news WHERE date(fetch_date) = ?', (today,))
    today_news = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM sources WHERE active = 1')
    active_sources = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM news')
    total_news = cursor.fetchone()[0]

    # آخر تحديث
    cursor.execute('SELECT MAX(fetch_date) FROM news')
    last_update = cursor.fetchone()[0]

    conn.close()

    return jsonify({
        'today_news': today_news,
        'active_sources': active_sources,
        'total_news': total_news,
        'last_update': last_update
    })

@app.route('/add_source', methods=['GET', 'POST'])
def add_source():
    """إضافة مصدر جديد"""
    if request.method == 'POST':
        name = request.form.get('name')
        url = request.form.get('url')
        source_type = request.form.get('type')
        category = request.form.get('category', 'عام')
        description = request.form.get('description', '')

        if not all([name, url, source_type]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            return redirect(url_for('add_source'))

        conn = sqlite3.connect('iraq_news.db')
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO sources (name, url, type, category, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, url, source_type, category, description))
            conn.commit()
            flash(f'تم إضافة المصدر "{name}" بنجاح', 'success')
            return redirect(url_for('admin'))
        except sqlite3.IntegrityError:
            flash('هذا الرابط موجود مسبقاً', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
        finally:
            conn.close()

    return render_template('add_source.html')

@app.route('/delete_source/<int:source_id>', methods=['POST'])
def delete_source(source_id):
    """حذف مصدر"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()

    # جلب اسم المصدر للرسالة
    cursor.execute('SELECT name FROM sources WHERE id = ?', (source_id,))
    source = cursor.fetchone()

    if source:
        cursor.execute('DELETE FROM sources WHERE id = ?', (source_id,))
        conn.commit()
        flash(f'تم حذف المصدر "{source[0]}" بنجاح', 'success')
    else:
        flash('المصدر غير موجود', 'error')

    conn.close()
    return redirect(url_for('admin'))

@app.route('/toggle_source/<int:source_id>', methods=['POST'])
def toggle_source(source_id):
    """تفعيل/إلغاء تفعيل مصدر"""
    conn = sqlite3.connect('iraq_news.db')
    cursor = conn.cursor()

    cursor.execute('SELECT name, active FROM sources WHERE id = ?', (source_id,))
    source = cursor.fetchone()

    if source:
        new_status = 0 if source[1] else 1
        cursor.execute('UPDATE sources SET active = ? WHERE id = ?', (new_status, source_id))
        conn.commit()

        status_text = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
        flash(f'{status_text} المصدر "{source[0]}"', 'success')
    else:
        flash('المصدر غير موجود', 'error')

    conn.close()
    return redirect(url_for('admin'))

@app.route('/api/sources')
def api_sources():
    """API للمصادر"""
    try:
        conn = sqlite3.connect('iraq_news.db')
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM sources ORDER BY name')
        sources_data = cursor.fetchall()

        sources = []
        for source in sources_data:
            sources.append({
                'id': source[0],
                'name': source[1],
                'url': source[2],
                'type': source[3],
                'category': source[4],
                'active': bool(source[5]),
                'icon_url': source[6],
                'description': source[7],
                'last_fetch': source[8],
                'fetch_count': source[9],
                'success_count': source[10]
            })

        conn.close()

        return jsonify({
            'success': True,
            'sources': sources
        })
    except Exception as e:
        logger.error(f"خطأ في API المصادر: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

if __name__ == '__main__':
    # إنشاء قاعدة البيانات
    init_database()

    # تشغيل الخادم
    try:
        logger.info("بدء تشغيل موسوعة أخبار العراق...")
        logger.info("الخادم متاح على: http://localhost:5008")

        app.run(host='0.0.0.0', port=5008, debug=True)

    except Exception as e:
        logger.error(f"خطأ في تشغيل الخادم: {e}")
    finally:
        logger.info("تم إيقاف الخادم")
