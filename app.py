from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, g
from requests_cache import CachedSession
import sqlite3
import requests
from bs4 import BeautifulSoup
import feedparser
from datetime import datetime, date, timedelta
import re
import json
import threading
import time
from dateutil import parser as date_parser
import locale
import logging
from logging.handlers import RotatingFileHandler
import os
from concurrent.futures import ThreadPoolExecutor
import schedule
from datetime import timedelta

# إعداد نظام التسجيل
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler = RotatingFileHandler(os.path.join(log_dir, 'app.log'), maxBytes=10000000, backupCount=5)
handler.setFormatter(formatter)

logger = logging.getLogger('IraqNewsApp')
logger.setLevel(logging.INFO)
logger.addHandler(handler)

# إعداد المُنفذ المتوازي للعمليات
thread_pool = ThreadPoolExecutor(max_workers=4)

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# ========================================
# أداة الأخبار العراقية الذكية - نصوص فقط
# ========================================
#
# تعليمات النظام:
# - جلب الأخبار النصية فقط (بدون صور أو فيديوهات)
# - التركيز على العناوين والمحتوى النصي
# - تصفية ذكية للأخبار العراقية
# - استبعاد المحافظات الكردية
# - استبعاد أخبار الفن والرياضة والطقس
# - عرض أخبار اليوم الحالي فقط
# - تصنيف تلقائي إلى 32 فئة
# - نظام أولويات (عالية، متوسطة، عادية)
# ========================================

# إعداد قاعدة البيانات
def init_db():
    conn = sqlite3.connect('news_database.db')
    cursor = conn.cursor()

    # إنشاء الجداول إذا لم تكن موجودة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sources (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            url TEXT NOT NULL,
            type TEXT NOT NULL,
            active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS news (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT,
            url TEXT,
            source_id INTEGER,
            published_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_iraq_related INTEGER DEFAULT 0,
            category TEXT DEFAULT 'عام',
            priority TEXT DEFAULT 'عادية',
            FOREIGN KEY (source_id) REFERENCES sources (id)
        )
    ''')

    # إضافة الفهارس لتحسين الأداء
    try:
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_published_date ON news(published_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_url ON news(url)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_title ON news(title)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_source_id ON news(source_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_is_iraq_related ON news(is_iraq_related)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sources_active ON sources(active)')
        logger.info('تم إنشاء الفهارس بنجاح')
    except sqlite3.Error as e:
        logger.error(f'خطأ في إنشاء الفهارس: {str(e)}')

    conn.commit()
    conn.close()

# الكلمات المفتاحية للتصفية
IRAQ_KEYWORDS = [
    'العراق', 'بغداد', 'العراقي', 'العراقية'
]

# تصنيفات الأخبار (مبسطة)
NEWS_CATEGORIES = {}
EXCLUDED_KEYWORDS = []

def is_iraq_related(text):
    """تحقق مما إذا كان النص يتعلق بالعراق"""
    text = text.lower()
    return any(keyword.lower() in text for keyword in IRAQ_KEYWORDS)

from textblob import TextBlob

def classify_news(text):
    """تصنيف الخبر بناءً على محتواه باستخدام تحليل المشاعر والكلمات المفتاحية"""
    # تحليل المشاعر
    blob = TextBlob(text)
    sentiment = blob.sentiment.polarity
    
    # تصنيف بناءً على المشاعر والكلمات المفتاحية
    if sentiment < -0.5:
        return 'أخبار سلبية'
    elif sentiment > 0.5:
        return 'أخبار إيجابية'
    
    # تصنيف بناءً على الكلمات المفتاحية
    for category, keywords in NEWS_CATEGORIES.items():
        if any(keyword in text.lower() for keyword in keywords):
            return category
            
    return 'عام' # الفئة الافتراضية

def get_news_priority(category):
    """تحديد أولوية الخبر"""
    return 'عادية'

# تعريف أنماط التواريخ العربية والإنجليزية
ARABIC_MONTHS = {
    'يناير': 1, 'كانون الثاني': 1,
    'فبراير': 2, 'شباط': 2,
    'مارس': 3, 'آذار': 3,
    'أبريل': 4, 'نيسان': 4,
    'مايو': 5, 'أيار': 5,
    'يونيو': 6, 'حزيران': 6,
    'يوليو': 7, 'تموز': 7,
    'أغسطس': 8, 'آب': 8,
    'سبتمبر': 9, 'أيلول': 9,
    'أكتوبر': 10, 'تشرين الأول': 10,
    'نوفمبر': 11, 'تشرين الثاني': 11,
    'ديسمبر': 12, 'كانون الأول': 12
}

def parse_arabic_text(text):
    """تحويل أسماء الأشهر العربية إلى أرقام"""
    if not isinstance(text, str):
        return text
    for month_ar, month_num in ARABIC_MONTHS.items():
        if month_ar in text:
            text = text.replace(month_ar, str(month_num))
    return text

def parse_relative_date(text):
    """تحليل التواريخ النسبية مثل 'منذ يومين'"""
    today = date.today()
    if not isinstance(text, str):
        return None
        
    if 'منذ' in text:
        if any(word in text for word in ['ساعة', 'ساعات', 'دقيقة', 'دقائق']):
            return today
        elif 'يوم' in text or 'أيام' in text:
            match = re.search(r'منذ (\d+)', text)
            if match:
                days = int(match.group(1))
                return today - timedelta(days=days)
    return None

def extract_date_from_entry(entry):
    """استخراج التاريخ من عنصر RSS"""
    dates = []
    
    # محاولة استخراج التاريخ من الحقول المُحللة
    for attr in ['published_parsed', 'updated_parsed']:
        if hasattr(entry, attr):
            parsed = getattr(entry, attr)
            if parsed:
                try:
                    dates.append(datetime(*parsed[:6]).date())
                except:
                    continue
    
    # محاولة استخراج التاريخ من النصوص
    for attr in ['published', 'updated', 'date', 'created', 'modified']:
        if hasattr(entry, attr):
            date_str = getattr(entry, attr)
            if isinstance(date_str, str):
                # تحويل التاريخ العربي
                date_str = parse_arabic_text(date_str)
                
                try:
                    # محاولة تحليل التاريخ النسبي
                    relative_date = parse_relative_date(date_str)
                    if relative_date:
                        dates.append(relative_date)
                    else:
                        # محاولة تحليل التاريخ المطلق
                        dates.append(date_parser.parse(date_str).date())
                except:
                    continue
                    
    return dates

def extract_date_from_html(soup):
    """استخراج التاريخ من صفحة HTML"""
    dates = []
    
    # البحث في meta tags
    meta_tags = [
        'article:published_time', 'article:modified_time',
        'og:updated_time', 'publish-date', 'date',
        'pubdate', 'datePublished', 'dateModified'
    ]
    
    for tag in meta_tags:
        meta = soup.find('meta', attrs={'property': tag}) or soup.find('meta', attrs={'name': tag})
        if meta and meta.get('content'):
            try:
                dates.append(date_parser.parse(meta['content']).date())
            except:
                continue
                
    # البحث في عناصر HTML
    time_elements = soup.find_all(['time', 'span', 'div'], 
        class_=lambda x: x and any(c in x.lower() for c in ['date', 'time', 'published', 'post-meta']))
    
    for element in time_elements:
        date_text = element.get('datetime') or element.get_text()
        if date_text:
            date_text = parse_arabic_text(date_text)
            try:
                relative_date = parse_relative_date(date_text)
                if relative_date:
                    dates.append(relative_date)
                else:
                    dates.append(date_parser.parse(date_text).date())
            except:
                continue
                
    return dates

def extract_news_date(entry=None, soup=None, url=None):
    """استخراج التاريخ الفعلي للخبر من المصدر"""
    try:
        today = date.today()
        dates = []
        
        # جمع التواريخ من RSS
        if entry:
            # التحقق من الحقول المباشرة
            for attr in ['published', 'updated', 'date', 'created', 'modified']:
                if hasattr(entry, attr):
                    try:
                        date_str = getattr(entry, attr)
                        # محاولة تحليل التاريخ بأشكال مختلفة
                        if isinstance(date_str, str):
                            # تحويل الشهور العربية إلى أرقام
                            date_str = parse_arabic_text(date_str)
                            # محاولة تحليل التاريخ النسبي (مثل "منذ يومين")
                            relative_date = parse_relative_date(date_str)
                            if relative_date:
                                dates.append(relative_date)
                            else:
                                # محاولة تحليل التاريخ المطلق
                                parsed_date = date_parser.parse(date_str)
                                if isinstance(parsed_date, datetime):
                                    dates.append(parsed_date.date())
                                else:
                                    dates.append(parsed_date)
                    except Exception as e:
                        logger.debug(f"فشل تحليل {attr}: {str(e)}")
                        continue

            # التحقق من الحقول المُجزأة
            for attr in ['published_parsed', 'updated_parsed']:
                if hasattr(entry, attr):
                    parsed = getattr(entry, attr)
                    if parsed:
                        try:
                            dates.append(datetime(*parsed[:6]).date())
                        except Exception as e:
                            logger.debug(f"فشل تحليل {attr}: {str(e)}")
                            continue
            
        # جمع التواريخ من HTML
        if soup:
            # البحث عن meta tags
            meta_tags = [
                'article:published_time', 'article:modified_time',
                'og:updated_time', 'publish-date', 'date',
                'pubdate', 'datePublished', 'dateModified',
                'DC.date', 'DC.Date', 'dc:date'
            ]
            
            for tag in meta_tags:
                meta = soup.find('meta', attrs={'property': tag}) or soup.find('meta', attrs={'name': tag})
                if meta and meta.get('content'):
                    try:
                        date_str = meta['content']
                        parsed_date = date_parser.parse(date_str)
                        if isinstance(parsed_date, datetime):
                            dates.append(parsed_date.date())
                        else:
                            dates.append(parsed_date)
                    except Exception as e:
                        logger.debug(f"فشل تحليل meta tag {tag}: {str(e)}")
                        continue

            # البحث في نص الصفحة
            date_patterns = [
                r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',  # YYYY-MM-DD
                r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',  # DD-MM-YYYY
                r'منذ \d+ (دقيقة|ساعة|يوم|أيام|أسبوع|أسابيع)',  # منذ X وقت
            ]

            for pattern in date_patterns:
                matches = re.finditer(pattern, str(soup))
                for match in matches:
                    date_str = match.group()
                    try:
                        # تحليل التاريخ النسبي
                        relative_date = parse_relative_date(date_str)
                        if relative_date:
                            dates.append(relative_date)
                            continue

                        # تحليل التاريخ المطلق
                        parsed_date = date_parser.parse(date_str)
                        if isinstance(parsed_date, datetime):
                            dates.append(parsed_date.date())
                        else:
                            dates.append(parsed_date)
                    except Exception as e:
                        logger.debug(f"فشل تحليل النمط {pattern}: {str(e)}")
                        continue
            
        # تصفية وفرز التواريخ
        valid_dates = [d for d in dates if isinstance(d, date) and d <= today]
        if valid_dates:
            result_date = max(valid_dates)  # اختيار أحدث تاريخ
            logger.info(f"تم استخراج التاريخ: {result_date}")
            return result_date
            
        # إذا لم نجد تاريخاً صالحاً
        logger.warning("لم يتم العثور على تاريخ صالح، استخدام تاريخ اليوم")
        return today
        
    except Exception as e:
        logger.error(f"خطأ في استخراج التاريخ: {str(e)}")
        return today

def is_today_news(news_date):
    """التحقق مما إذا كان الخبر من اليوم"""
    try:
        today = date.today()
        if isinstance(news_date, str):
            news_date = date_parser.parse(news_date).date()
        # نقبل الأخبار من اليوم الحالي فقط
        return news_date == today
        
    except Exception as e:
        logger.error(f"خطأ في التحقق من تاريخ الخبر: {str(e)}")
        return False

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/sources')
def sources():
    conn = sqlite3.connect('news_database.db', timeout=30)
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM sources ORDER BY created_at DESC')
    sources_list = cursor.fetchall()
    conn.close()
    return render_template('sources.html', sources=sources_list)

@app.route('/news')
def news():
    conn = sqlite3.connect('news_database.db', timeout=30)
    cursor = conn.cursor()

    # جلب الأخبار العراقية لليوم فقط
    today = date.today().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT n.title, n.published_date, s.name as source_name, n.url
        FROM news n
        JOIN sources s ON n.source_id = s.id
        WHERE n.is_iraq_related = 1 AND n.published_date = ?
        ORDER BY n.created_at DESC
    ''', (today,))

    news_list = cursor.fetchall()
    conn.close()
    return render_template('news.html', news=news_list)

@app.route('/add_source', methods=['POST'])
def add_source():
    name = request.form.get('name')
    url = request.form.get('url')
    source_type = request.form.get('type')

    if not name or not url or not source_type:
        flash('جميع الحقول مطلوبة', 'error')
        return redirect(url_for('sources'))

    conn = sqlite3.connect('news_database.db', timeout=30)
    cursor = conn.cursor()
    cursor.execute('INSERT INTO sources (name, url, type) VALUES (?, ?, ?)',
                   (name, url, source_type))
    conn.commit()
    conn.close()

    flash('تم إضافة المصدر بنجاح', 'success')
    return redirect(url_for('sources'))

@app.route('/delete_source/<int:source_id>')
def delete_source(source_id):
    conn = sqlite3.connect('news_database.db', timeout=30)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM sources WHERE id = ?', (source_id,))
    conn.commit()
    conn.close()

    flash('تم حذف المصدر بنجاح', 'success')
    return redirect(url_for('sources'))

@app.route('/api/fetch-news', methods=['POST'])
def fetch_news():
    try:
        start_time = time.time()
        conn = sqlite3.connect('news_database.db', timeout=30)
        cursor = conn.cursor()

        # حذف الأخبار القديمة (أكثر من يوم واحد)
        today = date.today().strftime('%Y-%m-%d')
        cursor.execute('DELETE FROM news WHERE published_date < ?', (today,))
        deleted_count = cursor.rowcount
        if deleted_count > 0:
            logger.info(f"تم حذف {deleted_count} خبر قديم")

        # جلب المصادر المفعلة
        cursor.execute('SELECT * FROM sources WHERE active = 1')
        sources = cursor.fetchall()
        conn.close()

        def process_source(source):
            source_id, name, url, source_type, active, created_at = source
            source_start_time = time.time()
            try:
                logger.info(f"بدء معالجة المصدر: {name} ({url})")

                # تصحيح الرابط إذا لزم الأمر
                original_url = url
                if not url.startswith(('http://', 'https://', 'tg://', 't.me')):
                    url = 'https://' + url.lstrip('/')
                    logger.info(f"تم تصحيح الرابط من {original_url} إلى: {url}")

                count = 0
                if source_type == 'rss':
                    count = fetch_rss_news(source_id, url, today)
                elif source_type == 'website':
                    count = fetch_website_news(source_id, url, today)
                elif source_type == 'telegram':
                    count = fetch_telegram_news(source_id, url, today)
                elif source_type == 'facebook':
                    count = fetch_facebook_news(source_id, url, today)
                else:
                    logger.warning(f"نوع مصدر غير معروف: {source_type} للمصدر {name}")
                    return {'source_id': source_id, 'name': name, 'count': 0, 'time': 0, 'error': 'نوع مصدر غير معروف'}

                processing_time = time.time() - source_start_time
                logger.info(f"{source_type.upper()} - تم جلب {count} خبر من {name} في {processing_time:.2f} ثانية")

                return {
                    'source_id': source_id,
                    'name': name,
                    'type': source_type,
                    'count': count,
                    'time': processing_time,
                    'error': None
                }

            except Exception as e:
                processing_time = time.time() - source_start_time
                error_msg = str(e)
                logger.error(f"خطأ في جلب الأخبار من {name} ({url}): {error_msg}", exc_info=True)

                return {
                    'source_id': source_id,
                    'name': name,
                    'type': source_type,
                    'count': 0,
                    'time': processing_time,
                    'error': error_msg
                }

        # معالجة المصادر بشكل متوازي
        results = list(thread_pool.map(process_source, sources))
        total_count = sum(result['count'] for result in results)
        total_time = time.time() - start_time

        # إحصائيات مفصلة
        successful_sources = [r for r in results if r['error'] is None and r['count'] > 0]
        failed_sources = [r for r in results if r['error'] is not None]
        empty_sources = [r for r in results if r['error'] is None and r['count'] == 0]

        logger.info(f"انتهى جلب الأخبار - المجموع: {total_count} خبر في {total_time:.2f} ثانية")
        logger.info(f"المصادر الناجحة: {len(successful_sources)}, الفاشلة: {len(failed_sources)}, الفارغة: {len(empty_sources)}")

        return jsonify({
            'success': True,
            'count': total_count,
            'deleted_old': deleted_count,
            'total_time': round(total_time, 2),
            'sources_processed': len(sources),
            'successful_sources': len(successful_sources),
            'failed_sources': len(failed_sources),
            'empty_sources': len(empty_sources),
            'details': results
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الأخبار: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/stats')
def get_stats():
    conn = sqlite3.connect('news_database.db', timeout=30)
    cursor = conn.cursor()

    # عدد المصادر
    cursor.execute('SELECT COUNT(*) FROM sources WHERE active = 1')
    sources_count = cursor.fetchone()[0]

    # أخبار اليوم
    today = date.today().strftime('%Y-%m-%d')
    cursor.execute('SELECT COUNT(*) FROM news WHERE published_date = ?', (today,))
    today_news_count = cursor.fetchone()[0]

    # الأخبار العراقية
    cursor.execute('SELECT COUNT(*) FROM news WHERE is_iraq_related = 1 AND published_date = ?', (today,))
    iraq_news_count = cursor.fetchone()[0]

    # إحصائيات التصنيفات
    cursor.execute('''
        SELECT category, COUNT(*) as count
        FROM news
        WHERE published_date = ? AND is_iraq_related = 1
        GROUP BY category
        ORDER BY count DESC
    ''', (today,))
    categories_stats = cursor.fetchall()

    # إحصائيات الأولويات
    cursor.execute('''
        SELECT priority, COUNT(*) as count
        FROM news
        WHERE published_date = ? AND is_iraq_related = 1
        GROUP BY priority
        ORDER BY
            CASE priority
                WHEN 'عالية' THEN 1
                WHEN 'متوسطة' THEN 2
                WHEN 'عادية' THEN 3
                ELSE 4
            END
    ''', (today,))
    priorities_stats = cursor.fetchall()

    # آخر تحديث
    cursor.execute('SELECT MAX(created_at) FROM news')
    last_update = cursor.fetchone()[0]

    conn.close()

    return jsonify({
        'sources_count': sources_count,
        'today_news_count': today_news_count,
        'iraq_news_count': iraq_news_count,
        'categories_stats': categories_stats,
        'priorities_stats': priorities_stats,
        'last_update': last_update if last_update else 'لا يوجد',
        'current_date': today
    })

@app.route('/api/current-datetime')
def get_current_datetime():
    """إرجاع التاريخ والوقت الحالي"""
    now = datetime.now()
    today = date.today()

    return jsonify({
        'current_date': today.strftime('%Y-%m-%d'),
        'current_datetime': now.strftime('%Y-%m-%d %H:%M:%S'),
        'arabic_date': today.strftime('%Y-%m-%d'),
        'arabic_datetime': now.strftime('%Y-%m-%d %H:%M:%S'),
        'timestamp': now.timestamp()
    })

@app.route('/api/test-source', methods=['POST'])
def test_source():
    try:
        data = request.get_json()
        url = data.get('url')
        source_type = data.get('type')

        if not url or not source_type:
            return jsonify({'success': False, 'message': 'بيانات غير مكتملة'})

        if source_type == 'rss':
            # اختبار RSS Feed
            feed = feedparser.parse(url)
            if feed.bozo:
                return jsonify({'success': False, 'message': 'RSS Feed غير صالح'})

            count = len(feed.entries)
            return jsonify({'success': True, 'count': count, 'message': f'تم العثور على {count} عنصر'})

        elif source_type == 'website':
            # اختبار موقع الويب
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code != 200:
                return jsonify({'success': False, 'message': f'خطأ HTTP: {response.status_code}'})

            soup = BeautifulSoup(response.content, 'html.parser')
            headlines = soup.find_all(['h1', 'h2', 'h3'])
            count = len(headlines)

            return jsonify({'success': True, 'count': count, 'message': f'تم العثور على {count} عنوان'})

        else:
            # دعم اختبار مصادر فيسبوك
            if source_type == 'facebook':
                import re
                access_token = os.environ.get('FACEBOOK_ACCESS_TOKEN', None)
                if not access_token:
                    return jsonify({'success': False, 'message': 'يرجى ضبط FACEBOOK_ACCESS_TOKEN في متغيرات البيئة'})
                # استخراج معرف الصفحة من الرابط
                match = re.search(r'facebook.com/([A-Za-z0-9_.-]+)', url)
                if not match:
                    return jsonify({'success': False, 'message': 'رابط فيسبوك غير صالح. يجب أن يكون رابط صفحة عامة فقط'})
                page_id = match.group(1)
                # التأكد من أن الرابط ليس لمنشور أو مجموعة
                if any(x in page_id.lower() for x in ['posts', 'groups', 'watch', 'events', 'permalink', 'photo', 'videos', 'story']):
                    return jsonify({'success': False, 'message': 'يرجى وضع رابط صفحة عامة فقط وليس منشور أو مجموعة'})
                endpoint = f'https://graph.facebook.com/v19.0/{page_id}/posts'
                params = {
                    'access_token': access_token,
                    'fields': 'message,created_time,permalink_url',
                    'limit': 1
                }
                try:
                    response = requests.get(endpoint, params=params, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        count = len(data.get('data', []))
                        return jsonify({'success': True, 'count': count, 'message': f'تم العثور على {count} منشور (الصفحة صالحة)'})
                    elif response.status_code == 400:
                        return jsonify({'success': False, 'message': 'الصفحة غير متاحة أو access token غير صالح'})
                    elif response.status_code == 404:
                        return jsonify({'success': False, 'message': 'لم يتم العثور على الصفحة. تحقق من رابط الصفحة أو صلاحية access token'})
                    else:
                        return jsonify({'success': False, 'message': f'خطأ HTTP: {response.status_code} - {response.text}'})
                except Exception as e:
                    return jsonify({'success': False, 'message': f'خطأ في الاتصال بفيسبوك: {str(e)}'})

        return jsonify({'success': False, 'message': 'نوع مصدر غير مدعوم'})

    except requests.exceptions.Timeout:
        return jsonify({'success': False, 'message': 'انتهت مهلة الاتصال'})
    except requests.exceptions.ConnectionError:
        return jsonify({'success': False, 'message': 'خطأ في الاتصال بالمصدر'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ غير متوقع: {str(e)}'})

# إدارة قاعدة البيانات
import sqlite3
from flask import g
import threading

db_lock = threading.Lock()

def get_db():
    """تهيئة الاتصال بقاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(
            'news_database.db',
            timeout=30,
            check_same_thread=False
        )
        db.row_factory = sqlite3.Row
    return db

@app.teardown_appcontext
def close_db(exception):
    """إغلاق الاتصال بقاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()
        g._database = None

def execute_db(query, args=()):
    """تنفيذ استعلام قاعدة البيانات مع قفل"""
    with db_lock:
        conn = sqlite3.connect('news_database.db', timeout=30)
        try:
            cursor = conn.cursor()
            cursor.execute(query, args)
            
            if query.lower().startswith('select'):
                result = cursor.fetchall()
                logger.debug(f"تم تنفيذ استعلام SELECT: {query[:100]}...")
            else:
                conn.commit()
                result = cursor.rowcount
                logger.debug(f"تم تنفيذ استعلام تعديل: {query[:100]}... (تأثر {result} صف)")
            
            return result
        except sqlite3.Error as e:
            logger.error(f"خطأ في قاعدة البيانات: {str(e)}\nالاستعلام: {query[:100]}...")
            return None
        finally:
            conn.close()

def fetch_rss_news(source_id, url, today, max_retries=3):
    """جلب الأخبار من مصدر RSS مع إمكانية إعادة المحاولة"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/rss+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3'
    }
    
    logger.info(f"بدء جلب الأخبار من المصدر {source_id} - {url}")
    news_count = 0

    for attempt in range(max_retries):
        try:
            # محاولة جلب المحتوى مع زيادة مهلة الانتظار
            response = requests.get(url, headers=headers, timeout=15)
            response.encoding = 'utf-8'
            response.raise_for_status()
            
            # تحليل المحتوى كـ RSS
            feed = feedparser.parse(response.content)
            
            # التحقق من وجود أخطاء في التحليل
            if feed.bozo and feed.bozo_exception:
                logger.error(f"خطأ في تحليل RSS: {feed.bozo_exception}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                return 0

            # معالجة كل خبر
            for entry in feed.entries[:30]:  # زيادة عدد الأخبار إلى 30
                try:
                    # استخراج العنوان والمحتوى
                    title = clean_text(entry.get('title', '').strip())
                    content = clean_text(entry.get('summary', entry.get('description', '')).strip())
                    link = entry.get('link', '').strip()

                    # تجاهل الأخبار القصيرة جداً أو غير المتعلقة بالعراق
                    if len(title) < 10 or not is_iraq_related(title):
                        logger.debug(f"تم تجاهل خبر غير متعلق بالعراق: {title[:50]}...")
                        continue

                    # استخراج وتحليل التاريخ
                    news_date = extract_news_date(entry=entry)
                    
                    # تسجيل تفاصيل التاريخ للتصحيح
                    logger.info(f"تاريخ الخبر: {news_date}, العنوان: {title[:50]}")
                    
                    if not news_date:
                        logger.warning(f"لم يتم العثور على تاريخ للخبر: {title[:50]}")
                        news_date = date.today()
                    elif news_date > date.today():
                        logger.warning(f"تاريخ مستقبلي تم تصحيحه: {news_date} -> {date.today()}, {title[:50]}")
                        news_date = date.today()

                    # التحقق من أن الخبر من اليوم
                    if not is_today_news(news_date):
                        logger.debug(f"تم تجاهل خبر قديم من {news_date}: {title[:50]}...")
                        continue

                    # التحقق من عدم وجود الخبر مسبقاً
                    exists = execute_db('SELECT id FROM news WHERE url = ? OR title = ?', (link, title))
                    if exists:
                        logger.debug(f"الخبر موجود مسبقاً: {title[:50]}")
                        continue

                    # إضافة الخبر إلى قاعدة البيانات
                    result = execute_db('''
                        INSERT INTO news (title, content, url, source_id, published_date,
                                        is_iraq_related, category, priority)
                        VALUES (?, ?, ?, ?, ?, 1, ?, ?)
                    ''', (title, content, link, source_id, news_date.strftime('%Y-%m-%d'),
                          'عام', 'عادية'))

                    if result is not None:
                        news_count += 1
                        logger.info(f"تم إضافة خبر جديد: {title[:50]}")

                except Exception as e:
                    logger.error(f"خطأ في معالجة الخبر: {str(e)}")
                    continue

            # إذا نجحت العملية، نخرج من الحلقة
            break

        except Exception as e:
            logger.error(f"خطأ في جلب أو معالجة RSS: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue
            return 0

    logger.info(f"تم جلب {news_count} خبر من المصدر {source_id}")
    return news_count

def fetch_website_news(source_id, url, today, max_retries=3, max_pages=5):
    """جلب الأخبار من موقع ويب مع إمكانية تصفح صفحات متعددة (pagination)"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    logger.info(f"بدء جلب الأخبار من: {url}")
    news_count = 0
    visited_urls = set()  # لتجنب زيارة نفس الرابط عدة مرات

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=15)
            response.encoding = 'utf-8'
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن العناوين في عدة أنماط شائعة في المواقع العراقية
            news_selectors = [
                'article', '.news-item', '.post', '.article',
                '[class*="news"]', '[class*="article"]',
                '[id*="news"]', '[id*="article"]',
                '.entry', '.item'
            ]
            
            news_items = []
            for selector in news_selectors:
                items = soup.select(selector)
                if items:
                    news_items.extend(items)
                    logger.debug(f"تم العثور على {len(items)} عنصر باستخدام {selector}")

            # إذا لم نجد أي عناصر، نبحث عن العناوين مباشرة
            if not news_items:
                headlines = soup.find_all(['h1', 'h2', 'h3', 'h4'],
                    class_=lambda x: x and any(word in str(x).lower() 
                        for word in ['title', 'headline', 'news', 'article', 'post'])
                )
                news_items.extend(headlines)

            processed_urls = set()  # لتجنب تكرار نفس الخبر
            
            for item in news_items[:30]:  # نأخذ أحدث 30 خبر فقط
                try:
                    # البحث عن العنوان
                    title_element = (
                        item.find(['h1', 'h2', 'h3', 'h4']) or 
                        item.find(class_=lambda x: x and any(word in str(x).lower() 
                            for word in ['title', 'headline']))
                    )
                    
                    if not title_element:
                        continue

                    title = clean_text(title_element.get_text().strip())
                    
                    # تجاهل العناوين القصيرة
                    if len(title) < 10:
                        continue

                    # التحقق من أن الخبر يتعلق بالعراق
                    if not is_iraq_related(title):
                        continue

                    # استخراج الرابط
                    link_element = (
                        item.find('a', href=True) or
                        title_element.find_parent('a', href=True) or
                        title_element.find('a', href=True)
                    )
                    
                    if not link_element or not link_element.get('href'):
                        continue  # تجاهل الخبر إذا لم يوجد رابط

                    link = link_element['href']
                    if link.startswith('/'):
                        from urllib.parse import urljoin
                        link = urljoin(url, link)
                    
                    # تجنب تكرار نفس الرابط
                    if link in processed_urls:
                        continue
                    processed_urls.add(link)

                    # استخراج المحتوى والتاريخ من صفحة الخبر
                    try:
                        news_response = requests.get(link, headers=headers, timeout=10)
                        news_response.encoding = 'utf-8'
                        news_soup = BeautifulSoup(news_response.content, 'html.parser')
                        
                        # استخراج التاريخ
                        news_date = extract_news_date(soup=news_soup)
                        if not news_date:
                            news_date = date.today()
                        
                        # تسجيل التاريخ المستخرج للمراجعة
                        logger.info(f"تاريخ الخبر المستخرج: {news_date}, العنوان: {title[:50]}")
                        
                        if not is_today_news(news_date):
                            logger.debug(f"تجاهل خبر قديم: {title[:50]} - {news_date}")
                            continue

                        # استخراج المحتوى
                        content = ""
                        content_element = news_soup.find(
                            ['article', 'div', 'section'],
                            class_=lambda x: x and any(word in str(x).lower() 
                                for word in ['content', 'article-body', 'entry-content', 'post-content'])
                        )
                        
                        if content_element:
                            content = clean_text(content_element.get_text())

                        # التحقق من عدم وجود الخبر مسبقاً
                        exists = execute_db('SELECT id FROM news WHERE url = ? OR title = ?', (link, title))
                        if exists:
                            continue

                        # حفظ الخبر في قاعدة البيانات
                        result = execute_db('''
                            INSERT INTO news (title, content, url, source_id, published_date,
                                            is_iraq_related, category, priority)
                            VALUES (?, ?, ?, ?, ?, 1, ?, ?)
                        ''', (title, content, link, source_id, news_date.strftime('%Y-%m-%d'),
                              'عام', 'عادية'))

                        if result is not None:
                            news_count += 1
                            logger.info(f"تم إضافة خبر جديد: {title[:50]}")

                    except Exception as e:
                        logger.error(f"خطأ في معالجة صفحة الخبر {link}: {str(e)}")
                        continue

                except Exception as e:
                    logger.error(f"خطأ في معالجة عنصر خبر: {str(e)}")
                    continue
            
            # البحث عن روابط الصفحات التالية (pagination)
            if len(news_items) >= 30:
                next_page = None
                
                # محاولة العثور على رابط الصفحة التالية
                pagination_selectors = [
                    'a.next', 'a[rel="next"]', 'a.pagination-next',
                    'a.page-next', 'a[aria-label="Next"]'
                ]
                
                for selector in pagination_selectors:
                    link_element = soup.select_one(selector)
                    if link_element and link_element.get('href'):
                        next_page = link_element['href']
                        break

                if next_page:
                    logger.info(f"تم العثور على صفحة جديدة: {next_page}")
                    if next_page.startswith('/'):
                        from urllib.parse import urljoin
                        next_page = urljoin(url, next_page)
                    
                    # استدعاء الدالة لجلب الأخبار من الصفحة التالية
                    sub_news_count = fetch_website_news(source_id, next_page, today, max_retries, max_pages-1)
                    news_count += sub_news_count
                    logger.info(f"تم جلب {sub_news_count} خبر من الصفحة التالية")
            
            # إذا نجحت العملية نخرج من الحلقة
            break
            
        except Exception as e:
            logger.error(f"خطأ في جلب الموقع: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue
    
    logger.info(f"تم جلب {news_count} خبر من المصدر {source_id}")
    return news_count

# إعداد متغيرات Telethon لجلب أخبار تلغرام
TELEGRAM_API_ID = os.environ.get('TELEGRAM_API_ID', '26246078')
TELEGRAM_API_HASH = os.environ.get('TELEGRAM_API_HASH', '7142c4e485040fb033eca364bbc7e2ce')
TELEGRAM_CHANNEL_USERNAME = os.environ.get('TELEGRAM_CHANNEL_USERNAME', None)  # يمكن ضبطه لكل مصدر

# --- جلب أخبار تلغرام فعلياً ---
def fetch_telegram_news(source_id, url, today, max_messages=20):
    """
    جلب الأخبار من قناة تلغرام باستخدام Telethon
    url: رابط القناة (مثلاً https://t.me/iraqnews)
    يجب ضبط TELEGRAM_API_ID و TELEGRAM_API_HASH في الأعلى.
    """
    try:
        from telethon import TelegramClient
        from telethon.errors.rpcerrorlist import ChannelInvalidError
        from telethon.tl.functions.messages import GetHistoryRequest
        import asyncio
        
        # استخراج اسم القناة من الرابط
        if url.startswith('https://t.me/'):
            channel_username = url.split('https://t.me/')[-1].strip('/')
        else:
            channel_username = url
        
        # تهيئة عميل Telethon
        session_name = f'telethon_session_{source_id}'
        client = TelegramClient(session_name, TELEGRAM_API_ID, TELEGRAM_API_HASH)
        news_count = 0
        
        async def fetch():
            nonlocal news_count
            await client.start()
            try:
                entity = await client.get_entity(channel_username)
                history = await client(GetHistoryRequest(
                    peer=entity,
                    limit=max_messages,
                    offset_date=None,
                    offset_id=0,
                    max_id=0,
                    min_id=0,
                    add_offset=0,
                    hash=0
                ))
                for message in history.messages:
                    if not message.message:
                        continue
                    title = clean_text(message.message.split('\n')[0])
                    content = clean_text(message.message)
                    # تجاهل العناوين القصيرة أو غير العراقية
                    if len(title) < 10 or not is_iraq_related(title):
                        continue
                    # تاريخ النشر
                    news_date = message.date.date()
                    if not is_today_news(news_date):
                        continue
                    # تحقق من عدم وجود الخبر مسبقاً
                    exists = execute_db('SELECT id FROM news WHERE title = ? AND source_id = ?', (title, source_id))
                    if exists:
                        continue
                    # حفظ الخبر
                    result = execute_db('''
                        INSERT INTO news (title, content, url, source_id, published_date, is_iraq_related, category, priority)
                        VALUES (?, ?, ?, ?, ?, 1, ?, ?)
                    ''', (title, content, url, source_id, news_date.strftime('%Y-%m-%d'), 'عام', 'عادية'))
                    if result is not None:
                        news_count += 1
            except ChannelInvalidError:
                logger.error(f"قناة تلغرام غير صالحة: {url}")
            except Exception as e:
                logger.error(f"خطأ في جلب تلغرام: {str(e)}")
            finally:
                await client.disconnect()
        
        asyncio.run(fetch())
        logger.info(f"تم جلب {news_count} خبر من تلغرام: {url}")
        return news_count
    except ImportError:
        logger.error("يجب تثبيت مكتبة telethon: pip install telethon")
        return 0
    except Exception as e:
        logger.error(f"خطأ عام في fetch_telegram_news: {str(e)}")
        return 0

# تنظيف النص
def clean_text(text):
    """تنظيف النص من العلامات HTML والرموز غير المرغوب فيها"""
    if not text:
        return ''
    # إزالة علامات HTML
    text = re.sub(r'<[^>]+>', ' ', text)
    # إزالة الرموز الخاصة
    text = re.sub(r'[\n\r\t]+', ' ', text)
    # إزالة المسافات الزائدة
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def get_news():
    """جلب الأخبار من جميع المصادر في قاعدة البيانات"""
    try:
        news_list = []
        conn = sqlite3.connect('news_database.db')
        cursor = conn.cursor()
        
        # جلب المصادر النشطة من قاعدة البيانات
        cursor.execute('SELECT id, name, url, type FROM sources WHERE active = 1')
        sources = cursor.fetchall()
        
        for source_id, name, url, source_type in sources:
            try:
                if source_type == 'rss':
                    feed = feedparser.parse(url)
                    for entry in feed.entries:
                        try:
                            # استخراج التاريخ
                            news_date = extract_news_date(entry)
                            if not is_today_news(news_date):
                                continue
                                
                            # استخراج العنوان والوصف
                            title = clean_text(entry.get('title', ''))
                            description = clean_text(entry.get('description', ''))
                            
                            # تحقق من أن الخبر يتعلق بالعراق
                            if not is_iraq_related(title) and not is_iraq_related(description):
                                continue

                            news = {
                                'title': title,
                                'description': description,
                                'link': entry.get('link', ''),
                                'date': news_date,
                                'source': name
                            }
                            news_list.append(news)
                            
                        except Exception as e:
                            logger.error(f"خطأ في معالجة الخبر: {str(e)}")
                            continue
                
                elif source_type == 'website':
                    # جلب الأخبار من المواقع التي لا تدعم RSS
                    try:
                        site_news = fetch_website_news(source_id, url, date.today().strftime('%Y-%m-%d'))
                        news_list.extend(site_news)
                    except Exception as e:
                        logger.error(f"خطأ في جلب الأخبار من الموقع {name}: {str(e)}")
                        continue
                            
            except Exception as e:
                logger.error(f"خطأ في تحليل المصدر {name}: {str(e)}")
                continue
        
        conn.close()
        return sorted(news_list, key=lambda x: x['date'], reverse=True)
        
    except Exception as e:
        logger.error(f"خطأ عام في جمع الأخبار: {str(e)}")
        return []

@app.route('/api/clean-news', methods=['POST'])
def clean_news():
    try:
        conn = sqlite3.connect('news_database.db', timeout=30)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM news')
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        logger.info(f"تم حذف جميع الأخبار ({deleted_count}) بنجاح")
        return jsonify({'success': True, 'deleted': deleted_count})
    except Exception as e:
        logger.error(f"خطأ في تنظيف الأخبار: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

def fetch_facebook_news(source_id, url, today, max_posts=20):
    """
    جلب الأخبار من صفحة فيسبوك باستخدام Facebook Graph API
    يتطلب ضبط FACEBOOK_ACCESS_TOKEN في متغيرات البيئة
    """
    import requests
    import re
    from datetime import datetime, date

    try:
        # الحصول على access token من متغيرات البيئة
        access_token = os.environ.get('FACEBOOK_ACCESS_TOKEN', None)
        if not access_token:
            logger.error("FACEBOOK_ACCESS_TOKEN غير موجود في متغيرات البيئة")
            return 0

        # استخراج معرف الصفحة من الرابط
        if url.startswith('https://facebook.com/') or url.startswith('https://www.facebook.com/'):
            match = re.search(r'facebook\.com/([A-Za-z0-9_.-]+)', url)
            if not match:
                logger.error(f"رابط فيسبوك غير صالح: {url}")
                return 0
            page_id = match.group(1)
        else:
            # إذا كان المعرف مباشرة
            page_id = url

        # التأكد من أن الرابط ليس لمنشور أو مجموعة
        if any(x in page_id.lower() for x in ['posts', 'groups', 'watch', 'events', 'permalink', 'photo', 'videos', 'story']):
            logger.error(f"رابط غير صالح - يجب أن يكون رابط صفحة عامة: {page_id}")
            return 0

        # إعداد طلب Graph API
        endpoint = f'https://graph.facebook.com/v19.0/{page_id}/posts'
        params = {
            'access_token': access_token,
            'fields': 'message,created_time,permalink_url,full_picture,story,description',
            'limit': max_posts,
            'since': today if isinstance(today, str) else today.strftime('%Y-%m-%d')
        }

        logger.info(f"جلب منشورات فيسبوك من: {page_id}")
        response = requests.get(endpoint, params=params, timeout=30)

        if response.status_code != 200:
            logger.error(f"خطأ في Facebook API: {response.status_code} - {response.text}")
            return 0

        data = response.json()
        posts = data.get('data', [])

        if not posts:
            logger.warning(f"لم يتم العثور على منشورات في الصفحة: {page_id}")
            return 0

        news_count = 0
        today_date = date.today() if not isinstance(today, date) else today

        for post in posts:
            try:
                # استخراج النص من المنشور
                message = post.get('message', '') or post.get('story', '') or post.get('description', '')
                if not message or len(message.strip()) < 20:
                    continue

                # تنظيف النص
                message = clean_text(message)
                title = message[:100] if len(message) > 100 else message

                # التحقق من أن المنشور يتعلق بالعراق
                if not is_iraq_related(message):
                    logger.debug(f"تجاهل منشور غير متعلق بالعراق: {title[:50]}")
                    continue

                # استخراج التاريخ
                created_time = post.get('created_time')
                if created_time:
                    try:
                        post_date = datetime.fromisoformat(created_time.replace('Z', '+00:00')).date()
                    except:
                        post_date = today_date
                else:
                    post_date = today_date

                # التحقق من أن المنشور من اليوم
                if not is_today_news(post_date):
                    logger.debug(f"تجاهل منشور قديم من {post_date}: {title[:50]}")
                    continue

                # الحصول على رابط المنشور
                post_url = post.get('permalink_url', url)

                # التحقق من عدم وجود المنشور مسبقاً
                exists = execute_db('SELECT id FROM news WHERE url = ? OR title = ?', (post_url, title))
                if exists:
                    logger.debug(f"المنشور موجود مسبقاً: {title[:50]}")
                    continue

                # حفظ المنشور في قاعدة البيانات
                result = execute_db('''
                    INSERT INTO news (title, content, url, source_id, published_date,
                                    is_iraq_related, category, priority)
                    VALUES (?, ?, ?, ?, ?, 1, ?, ?)
                ''', (title, message, post_url, source_id, post_date.strftime('%Y-%m-%d'),
                      classify_news(message), get_news_priority('عام')))

                if result is not None:
                    news_count += 1
                    logger.info(f"تم إضافة منشور فيسبوك: {title[:50]}")

            except Exception as e:
                logger.error(f"خطأ في معالجة منشور فيسبوك: {str(e)}")
                continue

        logger.info(f"تم جلب {news_count} منشور من فيسبوك: {page_id}")
        return news_count

    except Exception as e:
        logger.error(f"خطأ عام في جلب أخبار فيسبوك: {str(e)}")
        return 0

# نظام الجدولة التلقائية لجلب الأخبار
def auto_fetch_news():
    """جلب الأخبار تلقائياً"""
    try:
        logger.info("بدء الجلب التلقائي للأخبار")

        conn = sqlite3.connect('news_database.db', timeout=30)
        cursor = conn.cursor()

        # حذف الأخبار القديمة (أكثر من يوم واحد)
        today = date.today().strftime('%Y-%m-%d')
        cursor.execute('DELETE FROM news WHERE published_date < ?', (today,))
        deleted_count = cursor.rowcount
        if deleted_count > 0:
            logger.info(f"تم حذف {deleted_count} خبر قديم تلقائياً")

        # جلب المصادر المفعلة
        cursor.execute('SELECT * FROM sources WHERE active = 1')
        sources = cursor.fetchall()
        conn.close()

        total_count = 0
        for source in sources:
            source_id, name, url, source_type, active, created_at = source
            try:
                logger.info(f"جلب تلقائي من المصدر: {name}")

                if source_type == 'rss':
                    count = fetch_rss_news(source_id, url, today)
                elif source_type == 'website':
                    count = fetch_website_news(source_id, url, today)
                elif source_type == 'telegram':
                    count = fetch_telegram_news(source_id, url, today)
                elif source_type == 'facebook':
                    count = fetch_facebook_news(source_id, url, today)
                else:
                    count = 0

                total_count += count
                logger.info(f"تم جلب {count} خبر من {name}")

            except Exception as e:
                logger.error(f"خطأ في الجلب التلقائي من {name}: {str(e)}")
                continue

        logger.info(f"انتهى الجلب التلقائي - تم جلب {total_count} خبر إجمالي")

    except Exception as e:
        logger.error(f"خطأ في الجلب التلقائي: {str(e)}")

def run_scheduler():
    """تشغيل نظام الجدولة في خيط منفصل"""
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
        except Exception as e:
            logger.error(f"خطأ في نظام الجدولة: {str(e)}")
            time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ

# إعداد الجدولة
def setup_scheduler():
    """إعداد جدولة جلب الأخبار"""
    # جلب الأخبار كل 30 دقيقة
    schedule.every(30).minutes.do(auto_fetch_news)

    # جلب إضافي في أوقات الذروة
    schedule.every().day.at("08:00").do(auto_fetch_news)  # صباحاً
    schedule.every().day.at("12:00").do(auto_fetch_news)  # ظهراً
    schedule.every().day.at("18:00").do(auto_fetch_news)  # مساءً
    schedule.every().day.at("22:00").do(auto_fetch_news)  # ليلاً

    logger.info("تم إعداد نظام الجدولة التلقائية")

# تشغيل التطبيق
if __name__ == '__main__':
    # تهيئة قاعدة البيانات عند بدء التشغيل
    init_db()

    # إعداد وتشغيل نظام الجدولة
    setup_scheduler()
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    logger.info("تم تشغيل نظام الجدولة التلقائية")

    # جلب أولي للأخبار
    threading.Thread(target=auto_fetch_news, daemon=True).start()

    # تشغيل الخادم
    app.run(debug=True, host='0.0.0.0', port=5008)